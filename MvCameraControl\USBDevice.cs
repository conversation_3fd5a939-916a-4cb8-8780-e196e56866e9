using System;
using System.Runtime.InteropServices;

namespace MvCameraControl;

internal class USBDevice : Devi<PERSON>, IUSBDevice, IDevice, IDisposable
{
	public USBDevice(IDeviceInfo deviceInfo)
		: base(deviceInfo)
	{
	}

	/// <summary>
	/// 设置U3V的传输包大小 
	/// </summary>
	/// <param name="transferSize">传输的包大小,单位：Byte，默认为1M，范围：Windows[0x400, 0x400000], Linux[0x400, 0x200000]</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 增加传输包大小可以适当降低取流时的CPU占用率。但不同的PC和不同USB扩展卡存在不同的兼容性，如果该参数设置过大可能会出现取不到图像的风险。 
	/// </remarks>
	public int SetTransferSize(uint transferSize)
	{
		return MvCCDll.MV_USB_SetTransferSize(base.DevHandle, transferSize);
	}

	/// <summary>
	/// 获取U3V的传输包大小 
	/// </summary>
	/// <param name="transferSize">传输的包大小， 单位：Byte</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int GetTransferSize(out uint transferSize)
	{
		transferSize = 0u;
		return MvCCDll.MV_USB_GetTransferSize(base.DevHandle, ref transferSize);
	}

	/// <summary>
	/// 设置U3V的传输通道个数 
	/// </summary>
	/// <param name="transferWays">传输通道个数，范围：1-10 </param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 可根据PC的性能、设备出图帧率、图像大小和内存使用率等因素对该参数进行调节。但不同的PC和不同的USB扩展卡存在不同的兼容性。 
	/// </remarks>
	public int SetTransferWays(uint transferWays)
	{
		return MvCCDll.MV_USB_SetTransferWays(base.DevHandle, transferWays);
	}

	/// <summary>
	/// 获取U3V的传输通道个数
	/// </summary>
	/// <param name="transferWays">传输通道个数</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 该接口用于获取当前的U3V异步取流节点个数，U口相机传输通道个数和像素格式对应的负载包大小相关，通过最大异步注册长度 / 像素格式对应的负载包大小 计算得出。
	/// </remarks>
	public int GetTransferWays(out uint transferWays)
	{
		transferWays = 0u;
		return MvCCDll.MV_USB_GetTransferWays(base.DevHandle, ref transferWays);
	}

	/// <summary>
	/// 设置U3V的事件缓存节点个数 
	/// </summary>
	/// <param name="eventNodeNum">事件缓存节点个数，范围：1-64 </param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 该接口用于设置当前的U3V事件缓存节点个数，默认情况下为5个。
	/// </remarks>
	public int SetEventNodeNum(uint eventNodeNum)
	{
		return MvCCDll.MV_USB_SetEventNodeNum(base.DevHandle, eventNodeNum);
	}

	/// <summary>
	/// 设置U3V相机同步读写超时时间，范围为1000~UINT，默认1000 ms 
	/// </summary>
	/// <param name="timeoutInMS">同步读写超时时间</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int SetSyncTimeOut(uint timeoutInMS)
	{
		return MvCCDll.MV_USB_SetSyncTimeOut(base.DevHandle, timeoutInMS);
	}

	/// <summary>
	/// 获取U3V相机同步读写超时时间 
	/// </summary>
	/// <param name="timeoutInMS">同步读写超时时间</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int GetSyncTimeOut(out uint timeoutInMS)
	{
		timeoutInMS = 0u;
		return MvCCDll.MV_USB_GetSyncTimeOut(base.DevHandle, ref timeoutInMS);
	}

	/// <summary>
	/// 获取主机从USB设备接收的数据统计信息，如已接收字节数、帧数
	/// </summary>
	/// <param name="usbTransInfo">USB传输信息</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int GetUSBTransInfo(out USBTransInfo usbTransInfo)
	{
		usbTransInfo = default(USBTransInfo);
		MvCCDll.MV_ALL_MATCH_INFO pstInfo = default(MvCCDll.MV_ALL_MATCH_INFO);
		byte[] array = new byte[Marshal.SizeOf(typeof(MvCCDll.MV_MATCH_INFO_USB_DETECT))];
		MvCCDll.StructToBytes(default(MvCCDll.MV_MATCH_INFO_USB_DETECT), array);
		pstInfo.pInfo = Marshal.UnsafeAddrOfPinnedArrayElement((Array)array, 0);
		pstInfo.nType = 2u;
		pstInfo.nInfoSize = (uint)Marshal.SizeOf(typeof(MvCCDll.MV_MATCH_INFO_USB_DETECT));
		int num = MvCCDll.MV_CC_GetAllMatchInfo(base.DevHandle, ref pstInfo);
		if (num != 0)
		{
			return num;
		}
		MvCCDll.MV_MATCH_INFO_USB_DETECT mV_MATCH_INFO_USB_DETECT = (MvCCDll.MV_MATCH_INFO_USB_DETECT)Marshal.PtrToStructure(pstInfo.pInfo, typeof(MvCCDll.MV_MATCH_INFO_USB_DETECT));
		usbTransInfo.ReceiveDataSize = mV_MATCH_INFO_USB_DETECT.nReviceDataSize;
		usbTransInfo.ReceivedFrameCount = mV_MATCH_INFO_USB_DETECT.nRevicedFrameCount;
		usbTransInfo.ErrorFrameCount = mV_MATCH_INFO_USB_DETECT.nErrorFrameCount;
		return 0;
	}
}
