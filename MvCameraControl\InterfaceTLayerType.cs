using System;

namespace MvCameraControl;

/// <summary>
/// 采集卡接口类型定义
/// </summary>
[Flags]
public enum InterfaceTLayerType
{
	/// <summary>
	/// GigE Vision采集卡
	/// </summary>
	MvGigEInterface = 1,
	/// <summary>
	/// Camera Link采集卡
	/// </summary>
	MvCameraLinkInterface = 4,
	/// <summary>
	/// CoaXPress采集卡
	/// </summary>
	MvCXPInterface = 8,
	/// <summary>
	/// XoFLink采集卡
	/// </summary>
	MvXoFInterface = 0x10
}
