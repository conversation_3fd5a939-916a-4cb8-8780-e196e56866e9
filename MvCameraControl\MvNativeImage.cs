using System;
using System.Runtime.InteropServices;

namespace MvCameraControl;

/// <summary>
/// 从C库获取图像地址（GetImageBuffer或者回调），包装成Image，图像数据存放在非托管内存中
/// </summary>
internal class MvNativeImage : MvImage
{
	private IntPtr _pixelDataPtr = IntPtr.Zero;

	/// <summary>
	/// 非托管内存指针
	/// </summary>
	public override IntPtr PixelDataPtr => _pixelDataPtr;

	/// <summary>
	/// 图像数据，内部会进行一次拷贝，将非托管内存拷贝到托管内存
	/// </summary>
	public override byte[] PixelData
	{
		get
		{
			if (_pixelData == null)
			{
				_pixelData = new byte[ImageSize];
				Marshal.Copy(PixelDataPtr, _pixelData, 0, (int)ImageSize);
			}
			return _pixelData;
		}
	}

	/// <summary>
	/// 使用传入的非托管内存创建新的对象
	/// </summary>
	/// <param name="width"></param>
	/// <param name="height"></param>
	/// <param name="pixelType"></param>
	/// <param name="pixelDataPtr"></param>
	/// <param name="pixelDataLen"></param>
	/// <param name="devHandle">设备句柄</param>
	public MvNativeImage(uint width, uint height, MvGvspPixelType pixelType, IntPtr pixelDataPtr, ulong pixelDataLen, IntPtr devHandle)
	{
		_width = width;
		_height = height;
		_pixelType = pixelType;
		_pixelDataPtr = pixelDataPtr;
		_imageSize = pixelDataLen;
		_deviceHandle = devHandle;
	}

	~MvNativeImage()
	{
		Dispose(disposing: false);
	}

	protected override void Dispose(bool disposing)
	{
		if (!_isDisposed)
		{
			_isDisposed = true;
		}
	}
}
