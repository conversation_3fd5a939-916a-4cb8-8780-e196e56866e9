using System;
using System.Runtime.InteropServices;

namespace MvCameraControl;

internal class PixelTypeConverter : IPixelTypeConverter
{
	/// <summary>
	/// 设备句柄, 用于内部做格式转换
	/// </summary>
	private IntPtr _deviceHandle = IntPtr.Zero;

	private ByteBlockPool _byteBlockPool;

	/// <summary>
	/// 构造函数，内部创建内存池，只在ToBitmap中使用
	/// </summary>
	/// <param name="deviceHandle">设备句柄</param>
	public PixelTypeConverter(IntPtr deviceHandle)
	{
		_deviceHandle = deviceHandle;
		_byteBlockPool = new ByteBlockPool();
	}

	/// <summary>
	/// 构造函数
	/// </summary>
	/// <param name="deviceHandle">设备句柄</param>
	/// <param name="pool"></param>
	public PixelTypeConverter(IntPtr deviceHandle, ByteBlockPool pool)
	{
		_deviceHandle = deviceHandle;
		_byteBlockPool = pool;
	}

	/// <summary>
	/// 设置图像插值算法类型
	/// </summary>
	/// <param name="method">图像插值算法</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int SetBayerCvtQuality(CFAMethod method)
	{
		return MvCCDll.MV_CC_SetBayerCvtQuality(_deviceHandle, (uint)method);
	}

	/// <summary>
	/// 插值算法平滑使能设置
	/// </summary>
	/// <param name="enable">平滑使能（默认关闭）</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int SetBayerFilterEnable(bool enable)
	{
		byte bFilterEnable = (byte)(enable ? 1 : 0);
		return MvCCDll.MV_CC_SetBayerFilterEnable(_deviceHandle, bFilterEnable);
	}

	/// <summary>
	/// 设置Bayer格式的Gamma值
	/// </summary>
	/// <param name="gammaValue">Gamma值：0.1 ~ 4.0</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int SetBayerGammaValue(float gammaValue)
	{
		return MvCCDll.MV_CC_SetBayerGammaValue(_deviceHandle, gammaValue);
	}

	/// <summary>
	/// 设置Mono8/Bayer8/10/12/16格式的Gamma值
	/// </summary>
	/// <param name="pixelType">像素格式</param>
	/// <param name="gammaValue">Gamma值:0.1 ~ 4.0</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int SetGammaValue(MvGvspPixelType pixelType, float gammaValue)
	{
		return MvCCDll.MV_CC_SetGammaValue(_deviceHandle, (MvCCDll.MvGvspPixelType)pixelType, gammaValue);
	}

	/// <summary>
	/// 设置Bayer格式的Gamma信息
	/// </summary>
	/// <param name="gammaParam">Gamma参数</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int SetBayerGammaParam(GammaParam gammaParam)
	{
		MvCCDll.MV_CC_GAMMA_PARAM pstGammaParam = new MvCCDll.MV_CC_GAMMA_PARAM
		{
			enGammaType = (MvCCDll.MV_CC_GAMMA_TYPE)gammaParam.Type
		};
		if (gammaParam.Type == GammaType.Value)
		{
			pstGammaParam.fGammaValue = gammaParam.Value;
		}
		else if (gammaParam.Type == GammaType.UserCurve)
		{
			if (gammaParam.CurveBuf == null)
			{
				return -2147483644;
			}
			pstGammaParam.pGammaCurveBuf = Marshal.UnsafeAddrOfPinnedArrayElement((Array)gammaParam.CurveBuf, 0);
			pstGammaParam.nGammaCurveBufLen = gammaParam.CurveLen;
		}
		return MvCCDll.MV_CC_SetBayerGammaParam(_deviceHandle, ref pstGammaParam);
	}

	/// <summary>
	/// 设置Bayer格式的CCM使能和矩阵，量化系数默认1024
	/// </summary>
	/// <param name="ccmParam">CCM参数</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int SetBayerCCMParam(CCMParam ccmParam)
	{
		MvCCDll.MV_CC_CCM_PARAM_EX pstCCMParam = new MvCCDll.MV_CC_CCM_PARAM_EX
		{
			bCCMEnable = ccmParam.CCMEnable,
			nCCMScale = ccmParam.CCMScale,
			nCCMat = ccmParam.CCMat
		};
		return MvCCDll.MV_CC_SetBayerCCMParamEx(_deviceHandle, ref pstCCMParam);
	}

	/// <summary>
	/// 将图像转换为指定格式，输出IImage图像
	/// </summary>
	/// <param name="inImage">输入图像</param>
	/// <param name="outImage">输出图像</param>
	/// <param name="dstPixelType">目的像素格式</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int ConvertPixelType(IImage inImage, out IImage outImage, MvGvspPixelType dstPixelType)
	{
		outImage = null;
		if (inImage == null)
		{
			return -2147483644;
		}
		MvCCDll.MV_CC_PIXEL_CONVERT_PARAM_EX pstCvtParamEx = new MvCCDll.MV_CC_PIXEL_CONVERT_PARAM_EX
		{
			nWidth = inImage.Width,
			nHeight = inImage.Height,
			enSrcPixelType = (MvCCDll.MvGvspPixelType)inImage.PixelType,
			pSrcData = inImage.PixelDataPtr,
			nSrcDataLen = (uint)inImage.ImageSize
		};
		MvPooledImage mvPooledImage = new MvPooledImage(inImage.Width, inImage.Height, dstPixelType, _deviceHandle, _byteBlockPool);
		pstCvtParamEx.pDstBuffer = mvPooledImage.PixelDataPtr;
		pstCvtParamEx.nDstBufferSize = (uint)mvPooledImage.ImageSize;
		pstCvtParamEx.enDstPixelType = (MvCCDll.MvGvspPixelType)dstPixelType;
		int num = MvCCDll.MV_CC_ConvertPixelTypeEx(_deviceHandle, ref pstCvtParamEx);
		if (num != 0)
		{
			mvPooledImage.Dispose();
			return num;
		}
		mvPooledImage.ImageSize = pstCvtParamEx.nDstLen;
		outImage = mvPooledImage;
		return num;
	}

	/// <summary>
	/// 将图像转换为指定格式，输出Byte数组
	/// </summary>
	/// <param name="inImage">输入图像</param>
	/// <param name="outBuffer">输出图像缓存</param>
	/// <param name="outDataLen">输出图像长度</param>
	/// <param name="dstPixelType">目的像素格式</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// outBuffer设置为null时, outDataLen会给出结果图像需要的缓存长度
	/// </remarks>
	public int ConvertPixelType(IImage inImage, byte[] outBuffer, out ulong outDataLen, MvGvspPixelType dstPixelType)
	{
		outDataLen = 0uL;
		if (inImage == null)
		{
			return -2147483644;
		}
		MvCCDll.MV_CC_PIXEL_CONVERT_PARAM_EX pstCvtParamEx = new MvCCDll.MV_CC_PIXEL_CONVERT_PARAM_EX
		{
			nWidth = inImage.Width,
			nHeight = inImage.Height,
			enSrcPixelType = (MvCCDll.MvGvspPixelType)inImage.PixelType,
			pSrcData = inImage.PixelDataPtr,
			nSrcDataLen = (uint)inImage.ImageSize,
			enDstPixelType = (MvCCDll.MvGvspPixelType)dstPixelType
		};
		if (outBuffer == null)
		{
			pstCvtParamEx.pDstBuffer = IntPtr.Zero;
			pstCvtParamEx.nDstBufferSize = 0u;
		}
		else
		{
			pstCvtParamEx.pDstBuffer = Marshal.UnsafeAddrOfPinnedArrayElement((Array)outBuffer, 0);
			pstCvtParamEx.nDstBufferSize = (uint)outBuffer.Length;
		}
		int result = MvCCDll.MV_CC_ConvertPixelTypeEx(_deviceHandle, ref pstCvtParamEx);
		outDataLen = pstCvtParamEx.nDstLen;
		return result;
	}

	/// <summary>
	/// 获取像素格式转换所需的缓存大小
	/// </summary>
	/// <param name="dstPixelType">目标像素格式</param>
	/// <param name="width">图像宽</param>
	/// <param name="height">图像高</param>
	/// <returns>缓存大小</returns>
	public ulong GetBufferSizeForConvert(MvGvspPixelType dstPixelType, uint width, uint height)
	{
		return InnerTools.GetImageSize(width, height, dstPixelType);
	}

	public int ConvertPixelType(IntPtr srcBuffer, ImageInfo srcImageInfo, IntPtr dstBuffer, ulong dstBufferSize, MvGvspPixelType dstPixelType, out ImageInfo outImageInfo)
	{
		outImageInfo = new ImageInfo();
		if (srcBuffer == IntPtr.Zero || srcImageInfo == null)
		{
			return -2147483644;
		}
		MvCCDll.MV_CC_PIXEL_CONVERT_PARAM_EX pstCvtParamEx = new MvCCDll.MV_CC_PIXEL_CONVERT_PARAM_EX
		{
			nWidth = srcImageInfo.Width,
			nHeight = srcImageInfo.Height,
			enSrcPixelType = (MvCCDll.MvGvspPixelType)srcImageInfo.PixelType,
			pSrcData = srcBuffer,
			nSrcDataLen = (uint)srcImageInfo.ImageSize,
			enDstPixelType = (MvCCDll.MvGvspPixelType)dstPixelType,
			pDstBuffer = dstBuffer,
			nDstBufferSize = (uint)dstBufferSize
		};
		int num = MvCCDll.MV_CC_ConvertPixelTypeEx(_deviceHandle, ref pstCvtParamEx);
		outImageInfo.ImageSize = pstCvtParamEx.nDstLen;
		if (num != 0)
		{
			return num;
		}
		outImageInfo.Width = pstCvtParamEx.nWidth;
		outImageInfo.Height = pstCvtParamEx.nHeight;
		outImageInfo.PixelType = (MvGvspPixelType)pstCvtParamEx.enDstPixelType;
		return num;
	}
}
