{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"MvCameraControl.Net/1.0.0": {"dependencies": {"System.Drawing.Common": "9.0.7"}, "runtime": {"MvCameraControl.Net.dll": {}}}, "Microsoft.Win32.SystemEvents/9.0.7": {"runtime": {"lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/Microsoft.Win32.SystemEvents.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31616"}}}, "System.Drawing.Common/9.0.7": {"dependencies": {"Microsoft.Win32.SystemEvents": "9.0.7"}, "runtime": {"lib/net8.0/System.Drawing.Common.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31604"}, "lib/net8.0/System.Private.Windows.Core.dll": {"assemblyVersion": "9.0.0.0", "fileVersion": "9.0.725.31604"}}}}}, "libraries": {"MvCameraControl.Net/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Win32.SystemEvents/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-lFGY2aGgmMREPJEfOmZcA6v0CLjWVpcfNHRgqYMoSQhy80+GxhYqdW5xe+DCLrVqE1M7/0RpOkIo49/KH/cd/A==", "path": "microsoft.win32.systemevents/9.0.7", "hashPath": "microsoft.win32.systemevents.9.0.7.nupkg.sha512"}, "System.Drawing.Common/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-1k/Pk7hcM3vP2tfIRRS2ECCCN7ya+hvocsM1JMc4ZDCU6qw7yOuUmqmCDfgXZ4Q4FS6jass2EAai5ByKodDi0g==", "path": "system.drawing.common/9.0.7", "hashPath": "system.drawing.common.9.0.7.nupkg.sha512"}}}