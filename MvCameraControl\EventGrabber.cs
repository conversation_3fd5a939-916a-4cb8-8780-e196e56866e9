using System;

namespace MvCameraControl;

internal class EventGrabber : IEventGrabber
{
	private IntPtr _devHandle = IntPtr.Zero;

	private MvCCDll.cbEventdelegateEx _eventDelegate;

	public event EventHandler<DeviceEventArgs> DeviceEvent;

	public EventGrabber(IntPtr devHandle)
	{
		_devHandle = devHandle;
		_eventDelegate = EventDelegate;
	}

	public int SubscribeEvent(string eventName)
	{
		return MvCCDll.MV_CC_RegisterEventCallBackEx(_devHandle, eventName, _eventDelegate, IntPtr.Zero);
	}

	public int UnSubscribeEvent(string eventName)
	{
		return MvCCDll.MV_CC_RegisterEventCallBackEx(_devHandle, eventName, null, IntPtr.Zero);
	}

	public int SubscribeAllEvent()
	{
		return MvCCDll.MV_CC_RegisterAllEventCallBack(_devHandle, _eventDelegate, IntPtr.Zero);
	}

	public int UnSubscribeAllEvent()
	{
		return MvCCDll.MV_CC_RegisterAllEventCallBack(_devHandle, null, IntPtr.Zero);
	}

	private void EventDelegate(ref MvCCDll.MV_EVENT_OUT_INFO pEventInfo, IntPtr pUser)
	{
		EventHandler<DeviceEventArgs> eventHandler = this.DeviceEvent;
		if (eventHandler != null)
		{
			DeviceEventArgs e = new DeviceEventArgs(new EventOutInfo(pEventInfo));
			eventHandler(this, e);
		}
	}
}
