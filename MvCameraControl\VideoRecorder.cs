using System;

namespace MvCameraControl;

/// <summary>
/// 录像功能实现类
/// </summary>
internal class VideoRecorder : IVideoRecorder
{
	private IntPtr _devHandle;

	internal VideoRecorder(IntPtr devHandle)
	{
		_devHandle = devHandle;
	}

	public int InputOneFrame(IImage image)
	{
		if (image == null)
		{
			return -**********;
		}
		MvCCDll.MV_CC_INPUT_FRAME_INFO pstInputFrameInfo = new MvCCDll.MV_CC_INPUT_FRAME_INFO
		{
			pData = image.PixelDataPtr,
			nDataLen = (uint)image.ImageSize
		};
		return MvCCDll.MV_CC_InputOneFrame(_devHandle, ref pstInputFrameInfo);
	}

	public int StartRecord(string filePath, RecordParam recordParam)
	{
		MvCCDll.MV_CC_RECORD_PARAM pstRecordParam = new MvCCDll.MV_CC_RECORD_PARAM
		{
			strFilePath = filePath
		};
		if (recordParam.Width > 65535 || recordParam.Height > 65535)
		{
			return -**********;
		}
		pstRecordParam.nWidth = Convert.ToUInt16(recordParam.Width);
		pstRecordParam.nHeight = Convert.ToUInt16(recordParam.Height);
		pstRecordParam.enPixelType = (MvCCDll.MvGvspPixelType)recordParam.PixelType;
		pstRecordParam.nBitRate = recordParam.BitRate;
		pstRecordParam.fFrameRate = recordParam.FrameRate;
		pstRecordParam.enRecordFmtType = (MvCCDll.MV_RECORD_FORMAT_TYPE)recordParam.FormatType;
		return MvCCDll.MV_CC_StartRecord(_devHandle, ref pstRecordParam);
	}

	public int StopRecord()
	{
		return MvCCDll.MV_CC_StopRecord(_devHandle);
	}
}
