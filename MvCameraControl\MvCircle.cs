namespace MvCameraControl;

/// <summary>
/// 圆形
/// </summary>
public struct MvCircle
{
	/// <summary>
	/// 圆心
	/// </summary>
	public MvPoint CenterPoint;

	/// <summary>
	/// 宽向半径，根据图像的相对位置[0, 1.0]，半径与圆心的位置有关，需保证画出的圆在显示框范围之内，否则报错
	/// </summary>
	public float R1;

	/// <summary>
	/// 高向半径，根据图像的相对位置[0, 1.0]，半径与圆心的位置有关，需保证画出的圆在显示框范围之内，否则报错
	/// </summary>
	public float R2;

	/// <summary>
	/// 构造函数
	/// </summary>
	/// <param name="centerPoint">圆心</param>
	/// <param name="r1">宽向半径，根据图像的相对位置[0, 1.0]，半径与圆心的位置有关，需保证画出的圆在显示框范围之内，否则报错</param>
	/// <param name="r2">高向半径，根据图像的相对位置[0, 1.0]，半径与圆心的位置有关，需保证画出的圆在显示框范围之内，否则报错</param>
	public MvCircle(MvPoint centerPoint, float r1, float r2)
	{
		CenterPoint = centerPoint;
		R1 = r1;
		R2 = r2;
	}
}
