using System.Collections;
using System.Collections.Generic;

namespace MvCameraControl;

internal class ChunkInfo : IChunkInfo, IEnumerable<IChunkData>, IEnumerable
{
	private Dictionary<uint, IChunkData> _chunkDataList = new Dictionary<uint, IChunkData>();

	public IChunkData this[uint ChunkID]
	{
		get
		{
			if (_chunkDataList.ContainsKey(ChunkID))
			{
				return _chunkDataList[ChunkID];
			}
			return null;
		}
		set
		{
			_chunkDataList[value.ChunkID] = value;
		}
	}

	public int Count => _chunkDataList.Count;

	public IEnumerator<IChunkData> GetEnumerator()
	{
		return _chunkDataList.Values.GetEnumerator();
	}

	IEnumerator IEnumerable.GetEnumerator()
	{
		return _chunkDataList.Values.GetEnumerator();
	}
}
