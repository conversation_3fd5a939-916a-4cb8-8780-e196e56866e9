using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;

namespace MvCameraControl;

/// <summary>
/// 图像类，实现基本的图像属性和接口,实现图像克隆、ToBitmap。作为其他图像类的基类
/// </summary>
internal class MvImage : IImage, ICloneable, IDisposable
{
	protected byte[] _pixelData;

	protected uint _width;

	protected uint _height;

	protected MvGvspPixelType _pixelType = MvGvspPixelType.PixelType_Gvsp_Undefined;

	protected ulong _imageSize;

	protected IntPtr _deviceHandle = IntPtr.Zero;

	protected bool _isDisposed;

	/// <summary>
	/// 相机句柄，用于内部做格式转换等图像处理
	/// </summary>
	internal virtual IntPtr DeviceHandle
	{
		get
		{
			return _deviceHandle;
		}
		set
		{
			_deviceHandle = value;
		}
	}

	/// <summary>
	/// 非托管内存指针
	/// </summary>
	public virtual IntPtr PixelDataPtr => Marshal.UnsafeAddrOfPinnedArrayElement((Array)_pixelData, 0);

	/// <summary>
	/// 图像数据，内部会进行一次拷贝，将非托管内存拷贝到托管内存
	/// </summary>
	public virtual byte[] PixelData => _pixelData;

	public virtual uint Width
	{
		get
		{
			return _width;
		}
		internal set
		{
			_width = value;
		}
	}

	public virtual uint Height
	{
		get
		{
			return _height;
		}
		internal set
		{
			_height = value;
		}
	}

	public virtual MvGvspPixelType PixelType
	{
		get
		{
			return _pixelType;
		}
		internal set
		{
			_pixelType = value;
		}
	}

	public virtual ulong ImageSize
	{
		get
		{
			return _imageSize;
		}
		internal set
		{
			_imageSize = value;
		}
	}

	/// <summary>
	/// 根据图像宽、高和像素格式创建对象，内部申请内存
	/// </summary>
	/// <param name="width">图像宽度</param>
	/// <param name="height">图像高度</param>
	/// <param name="pixelType">像素格式</param>
	/// <param name="handle">相机句柄，用于ToBitmap时像素格式转换</param>
	public MvImage(uint width, uint height, MvGvspPixelType pixelType, IntPtr handle)
	{
		_width = width;
		_height = height;
		_pixelType = pixelType;
		ulong imageSize = InnerTools.GetImageSize(width, height, pixelType);
		_pixelData = new byte[imageSize];
		_imageSize = imageSize;
		_deviceHandle = handle;
	}

	/// <summary>
	/// 根据图像宽、高、像素格式、图像大小创建对象，内部申请内存
	/// </summary>
	/// <param name="width">图像宽度</param>
	/// <param name="height">图像高度</param>
	/// <param name="pixelType">像素格式</param>
	/// <param name="imageSize">图像大小</param>
	/// <param name="handle">相机句柄，用于ToBitmap时像素格式转换</param>
	public MvImage(uint width, uint height, MvGvspPixelType pixelType, ulong imageSize, IntPtr handle)
	{
		_width = width;
		_height = height;
		_pixelType = pixelType;
		_pixelData = new byte[imageSize];
		_imageSize = imageSize;
		_deviceHandle = handle;
	}

	/// <summary>
	/// 根据图像宽、高、像素格式、图像大小创建对象，外部传入内存
	/// </summary>
	/// <param name="width"></param>
	/// <param name="height"></param>
	/// <param name="pixelType"></param>
	/// <param name="imageSize"></param>
	/// <param name="buffer"></param>
	/// <param name="handle"></param>
	public MvImage(uint width, uint height, MvGvspPixelType pixelType, ulong imageSize, byte[] buffer, IntPtr handle)
	{
		_width = width;
		_height = height;
		_pixelType = pixelType;
		_imageSize = imageSize;
		_pixelData = buffer;
		_deviceHandle = handle;
	}

	/// <summary>
	/// 用于克隆
	/// </summary>
	/// <param name="image"></param>
	protected MvImage(MvImage image)
	{
		_width = image.Width;
		_height = image.Height;
		_pixelType = image.PixelType;
		ulong num = image.ImageSize;
		if (image.ImageSize < 85000)
		{
			num = 85000uL;
		}
		_pixelData = new byte[num];
		_imageSize = image.ImageSize;
		Marshal.Copy(image.PixelDataPtr, _pixelData, 0, (int)image.ImageSize);
		_deviceHandle = image.DeviceHandle;
	}

	/// <summary>
	/// 构造空对象，内部变量由由子类初始化
	/// </summary>
	protected MvImage()
	{
	}

	~MvImage()
	{
		Dispose(disposing: false);
	}

	protected virtual void Dispose(bool disposing)
	{
		if (!_isDisposed)
		{
			_isDisposed = true;
		}
	}

	public void Dispose()
	{
		Dispose(disposing: true);
		GC.SuppressFinalize(this);
	}

	public object Clone()
	{
		return new MvImage(this);
	}

    public Bitmap GetToBitmap()
    {
        if (InnerTools.IsHBPixelType(PixelType))
        {
            if (new ImageDecoder(_deviceHandle).HBDecode(this, out var outImage) == 0)
            {
                return outImage.GetToBitmap();
            }
            return null;
        }
        PixelFormat pixelFormat = PixelFormat.Undefined;
        if (InnerTools.IsMonoPixel(PixelType))
        {
            pixelFormat = PixelFormat.Format8bppIndexed;
        }
        else
        {
            if (!InnerTools.IsColorPixel(PixelType))
            {
                throw new MvException(-2147483647, "Unsupport PixelType");
            }
            pixelFormat = PixelFormat.Format24bppRgb;
        }
        try
        {
            Bitmap bitmap = new Bitmap((int)Width, (int)Height, pixelFormat);
            if (pixelFormat == PixelFormat.Format8bppIndexed)
            {
                ColorPalette palette = bitmap.Palette;
                for (int i = 0; i < palette.Entries.Length; i++)
                {
                    palette.Entries[i] = Color.FromArgb(i, i, i);
                }
                bitmap.Palette = palette;
            }
            IntPtr zero = IntPtr.Zero;
            ulong num = 0uL;
            IImage outImage2 = null;
            if (_pixelType != MvGvspPixelType.PixelType_Gvsp_Mono8 && _pixelType != MvGvspPixelType.PixelType_Gvsp_BGR8_Packed)
            {
                MvGvspPixelType mvGvspPixelType = MvGvspPixelType.PixelType_Gvsp_Undefined;
                mvGvspPixelType = ((!InnerTools.IsMonoPixel(_pixelType)) ? MvGvspPixelType.PixelType_Gvsp_BGR8_Packed : MvGvspPixelType.PixelType_Gvsp_Mono8);
                int num2 = ((IPixelTypeConverter)new PixelTypeConverter(_deviceHandle)).ConvertPixelType((IImage)this, out outImage2, mvGvspPixelType);
                if (num2 != 0)
                {
                    throw new MvException(num2, "ConvertPixelType failed, result: " + num2.ToString("X"));
                }
            }
            if (outImage2 != null)
            {
                zero = Marshal.UnsafeAddrOfPinnedArrayElement((Array)outImage2.PixelData, 0);
                num = outImage2.ImageSize;
            }
            else
            {
                zero = PixelDataPtr;
                num = ImageSize;
            }
            BitmapData bitmapData = bitmap.LockBits(new Rectangle(0, 0, (int)Width, (int)Height), ImageLockMode.ReadWrite, pixelFormat);
            InnerTools.CopyMemory(bitmapData.Scan0, zero, (uint)num);
            bitmap.UnlockBits(bitmapData);
            return bitmap;
        }
        catch (Exception)
        {
            return null;
        }
    }
}
