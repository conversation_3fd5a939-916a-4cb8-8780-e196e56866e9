namespace MvCameraControl;

/// <summary>
/// 设备event信息
/// </summary>
public interface IEventOutInfo
{
	/// <summary>
	/// Event名称
	/// </summary>
	string EventName { get; }

	/// <summary>
	/// EventID
	/// </summary>
	ushort EventID { get; }

	/// <summary>
	/// 流通道序号 
	/// </summary>
	ushort StreamChannel { get; }

	/// <summary>
	/// 帧号 (暂无固件支持)
	/// </summary>
	ulong BlockId { get; }

	/// <summary>
	/// 时间戳
	/// </summary>
	ulong Timestamp { get; }

	/// <summary>
	/// Event数据长度 (暂无固件支持)
	/// </summary>
	uint EventDataSize { get; }

	/// <summary>
	/// Event数据，内部会进行一次拷贝，将非托管内存拷贝到托管内存 (暂无固件支持)
	/// </summary>
	byte[] EventData { get; }
}
