using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace MvCameraControl;

internal static class GenTLManagerImpl
{
	public static int EnumInterfacesByGenTLImpl(string ctiPath, out List<IGenTLIFInfo> IFList)
	{
		IFList = new List<IGenTLIFInfo>();
		MvCCDll.MV_GENTL_IF_INFO_LIST pstIFInfoList = default(MvCCDll.MV_GENTL_IF_INFO_LIST);
		int num = MvCCDll.MV_CC_EnumInterfacesByGenTL(ref pstIFInfoList, ctiPath);
		if (num != 0)
		{
			return num;
		}
		for (int i = 0; i < pstIFInfoList.nInterfaceNum; i++)
		{
			MvCCDll.MV_GENTL_IF_INFO mV_GENTL_IF_INFO = (MvCCDll.MV_GENTL_IF_INFO)Marshal.PtrToStructure(pstIFInfoList.pIFInfo[i], typeof(MvCCDll.MV_GENTL_IF_INFO));
			GenTLIFInfo genTLIFInfo = new GenTLIFInfo();
			genTLIFInfo.CtiIndex = mV_GENTL_IF_INFO.nCtiIndex;
			genTLIFInfo.DisplayName = mV_GENTL_IF_INFO.chDisplayName;
			genTLIFInfo.InterfaceID = mV_GENTL_IF_INFO.chInterfaceID;
			genTLIFInfo.TLType = mV_GENTL_IF_INFO.chTLType;
			IFList.Add(genTLIFInfo);
		}
		return 0;
	}

	public static int EnumDevicesByGenTLImpl(IGenTLIFInfo IFInfo, out List<IGenTLDevInfo> devList)
	{
		devList = new List<IGenTLDevInfo>();
		MvCCDll.MV_GENTL_IF_INFO stIFInfo = new MvCCDll.MV_GENTL_IF_INFO
		{
			nCtiIndex = IFInfo.CtiIndex,
			chDisplayName = IFInfo.DisplayName,
			chInterfaceID = IFInfo.InterfaceID,
			chTLType = IFInfo.TLType
		};
		MvCCDll.MV_GENTL_DEV_INFO_LIST pstDevList = default(MvCCDll.MV_GENTL_DEV_INFO_LIST);
		int num = MvCCDll.MV_CC_EnumDevicesByGenTL(ref stIFInfo, ref pstDevList);
		if (num != 0)
		{
			return num;
		}
		for (int i = 0; i < pstDevList.nDeviceNum; i++)
		{
			MvCCDll.MV_GENTL_DEV_INFO_EX mV_GENTL_DEV_INFO_EX = (MvCCDll.MV_GENTL_DEV_INFO_EX)Marshal.PtrToStructure(pstDevList.pDeviceInfo[i], typeof(MvCCDll.MV_GENTL_DEV_INFO_EX));
			GenTLDevInfo genTLDevInfo = new GenTLDevInfo();
			genTLDevInfo.InterfaceID = mV_GENTL_DEV_INFO_EX.chInterfaceID;
			genTLDevInfo.DeviceID = mV_GENTL_DEV_INFO_EX.chDeviceID;
			genTLDevInfo.VendorName = mV_GENTL_DEV_INFO_EX.chVendorName;
			genTLDevInfo.ModelName = mV_GENTL_DEV_INFO_EX.chModelName;
			genTLDevInfo.TLType = mV_GENTL_DEV_INFO_EX.chTLType;
			genTLDevInfo.DisplayName = mV_GENTL_DEV_INFO_EX.chDisplayName;
			genTLDevInfo.UserDefinedName = InnerTools.ByteArrayToString(mV_GENTL_DEV_INFO_EX.chUserDefinedName);
			genTLDevInfo.SerialNumber = mV_GENTL_DEV_INFO_EX.chSerialNumber;
			genTLDevInfo.DeviceVersion = mV_GENTL_DEV_INFO_EX.chDeviceVersion;
			genTLDevInfo.CtiIndex = mV_GENTL_DEV_INFO_EX.nCtiIndex;
			devList.Add(genTLDevInfo);
		}
		return 0;
	}

	public static int UnloadGenTLLibraryImpl(string ctiPath)
	{
		return MvCCDll.MV_CC_UnloadGenTLLibrary(ctiPath);
	}
}
