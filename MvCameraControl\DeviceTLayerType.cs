using System;

namespace MvCameraControl;

/// <summary>
/// 设备接口类型
/// </summary>
[Flags]
public enum DeviceTLayerType
{
	/// <summary>GigE Vision 设备</summary>
	MvGigEDevice = 1,
	/// <summary>USB3 Vision 设备</summary>
	MvUsbDevice = 4,
	/// <summary>Camera Link 设备（串口）</summary>
	MvCameraLinkDevice = 8,
	/// <summary>虚拟 GigE Vision 设备</summary>
	MvVirGigEDevice = 0x10,
	/// <summary>虚拟 USB3 Vision 设备</summary>
	MvVirUsbDevice = 0x20,
	/// <summary>网口采集卡下GigE Vision设备</summary>
	MvGenTLGigEDevice = 0x40,
	/// <summary>Camera Link 设备</summary>
	MvGenTLCameraLinkDevice = 0x80,
	/// <summary>CoaXPress设备</summary>
	MvGenTLCXPDevice = 0x100,
	/// <summary>XoFLink设备</summary>
	MvGenTLXoFDevice = 0x200
}
