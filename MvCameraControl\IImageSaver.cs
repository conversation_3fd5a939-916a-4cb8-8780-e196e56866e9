using System;

namespace MvCameraControl;

/// <summary>
/// 提供保存图像数据到文件的接口，支持BMP、JPG、PNG、TIFF格式图像
/// </summary>
public interface IImageSaver
{
	/// <summary>
	/// 保存图像到文件，支持BMP、JPG、PNG、TIFF格式图像 
	/// </summary>
	/// <param name="filePath">文件路径</param>
	/// <param name="image">图像数据</param>
	/// <param name="imageFormatInfo">图像格式信息</param>
	/// <param name="cfaMethod">图像插值方法</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	int SaveImageToFile(string filePath, IImage image, ImageFormatInfo imageFormatInfo, CFAMethod cfaMethod);

	/// <summary>
	/// 保存图像到文件，支持BMP、JPG、PNG、TIFF格式图像
	/// </summary>
	/// <param name="filePath">文件路径</param>
	/// <param name="srcBuffer">输入图像缓存</param>
	/// <param name="imageInfo">输入图像信息</param>
	/// <param name="imageFormatInfo">图像格式信息</param>
	/// <param name="cfaMethod">图像插值方法</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	int SaveImageToFile(string filePath, IntPtr srcBuffer, ImageInfo imageInfo, ImageFormatInfo imageFormatInfo, CFAMethod cfaMethod);

	/// <summary>
	/// 保存图像到缓存，支持BMP、JPG格式图像
	/// </summary>
	/// <param name="buffer">图像缓存</param>
	/// <param name="dataLen">转换后的图像数据长度</param>
	/// <param name="image">图像数据</param>
	/// <param name="imageFormatInfo">图像格式信息</param>
	/// <param name="cfaMethod">图像插值方法</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	int SaveImageToBuffer(byte[] buffer, out uint dataLen, IImage image, ImageFormatInfo imageFormatInfo, CFAMethod cfaMethod);

	/// <summary>
	/// 保存图像到缓存，支持BMP、JPG格式图像
	/// </summary>
	/// <param name="dstBuffer">输出图像缓存</param>
	/// <param name="dataLen">输出图像长度</param>
	/// <param name="srcBuffer">输入图像缓存</param>
	/// <param name="imageInfo">输入图像信息</param>
	/// <param name="imageFormatInfo">图像格式信息</param>
	/// <param name="cfaMethod">图像插值方法</param>
	/// <returns></returns>
	int SaveImageToBuffer(byte[] dstBuffer, out ulong dataLen, IntPtr srcBuffer, ImageInfo imageInfo, ImageFormatInfo imageFormatInfo, CFAMethod cfaMethod);
}
