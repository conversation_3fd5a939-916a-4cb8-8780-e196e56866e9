using System;
using System.Runtime.InteropServices;

namespace MvCameraControl;

internal class StreamGrabber : IStreamGrabber
{
	private IntPtr _devHandle;

	private MvCCDll.cbOutputExdelegate _outputDelegate;

	private MvCCDll.cbStreamException _streamExceptionDelegate;

	private bool _outputDelegateIsRegistered;

	private bool _streamExceptionIsRegistered;

	private event EventHandler<FrameGrabbedEventArgs> _FrameGrabedEvent;

	/// <summary>
	/// USB流异常回调
	/// </summary>
	private event EventHandler<StreamExceptionEventArgs> _StreamExceptionEvent;

	public event EventHandler<StreamExceptionEventArgs> StreamExceptionEvent
	{
		add
		{
			_StreamExceptionEvent += value;
		}
		remove
		{
			_StreamExceptionEvent -= value;
		}
	}

	public event EventHandler<FrameGrabbedEventArgs> FrameGrabedEvent
	{
		add
		{
			_FrameGrabedEvent += value;
		}
		remove
		{
			_FrameGrabedEvent -= value;
		}
	}

	public StreamGrabber(IntPtr deviceHandle)
	{
		_devHandle = deviceHandle;
		this._FrameGrabedEvent = null;
		this._StreamExceptionEvent = null;
		_outputDelegate = OutputCallback;
		_streamExceptionDelegate = StreamExceptionCallback;
	}

	public int SetImageNodeNum(uint num)
	{
		return MvCCDll.MV_CC_SetImageNodeNum(_devHandle, num);
	}

	public int GetValidImageNum(out uint num)
	{
		num = 0u;
		return MvCCDll.MV_CC_GetValidImageNum(_devHandle, ref num);
	}

	public int StartGrabbing()
	{
		int num = 0;
		if (this._FrameGrabedEvent != null && !_outputDelegateIsRegistered)
		{
			num = MvCCDll.MV_CC_RegisterImageCallBackEx(_devHandle, _outputDelegate, IntPtr.Zero);
			if (num != 0)
			{
				return num;
			}
			_outputDelegateIsRegistered = true;
		}
		else if (this._FrameGrabedEvent == null && _outputDelegateIsRegistered)
		{
			num = MvCCDll.MV_CC_RegisterImageCallBackEx(_devHandle, null, IntPtr.Zero);
			if (num != 0)
			{
				return num;
			}
			_outputDelegateIsRegistered = false;
		}
		if (this._StreamExceptionEvent != null && !_streamExceptionIsRegistered)
		{
			num = MvCCDll.MV_USB_RegisterStreamExceptionCallBack(_devHandle, _streamExceptionDelegate, IntPtr.Zero);
			if (num != 0)
			{
				return num;
			}
			_streamExceptionIsRegistered = true;
		}
		else if (this._StreamExceptionEvent == null && _streamExceptionIsRegistered)
		{
			num = MvCCDll.MV_USB_RegisterStreamExceptionCallBack(_devHandle, null, IntPtr.Zero);
			if (num != 0)
			{
				return num;
			}
			_streamExceptionIsRegistered = false;
		}
		return MvCCDll.MV_CC_StartGrabbing(_devHandle);
	}

	public int StartGrabbing(StreamGrabStrategy strategy)
	{
		int num = MvCCDll.MV_CC_SetGrabStrategy(_devHandle, (MvCCDll.MV_GRAB_STRATEGY)strategy);
		if (num != 0)
		{
			return num;
		}
		return StartGrabbing();
	}

	public int SetOutputQueueSize(uint size)
	{
		MvCCDll.MV_CC_SetGrabStrategy(_devHandle, MvCCDll.MV_GRAB_STRATEGY.MV_GrabStrategy_LatestImages);
		return MvCCDll.MV_CC_SetOutputQueueSize(_devHandle, size);
	}

	public int StopGrabbing()
	{
		return MvCCDll.MV_CC_StopGrabbing(_devHandle);
	}

	public int GetImageBuffer(uint timeoutInMS, out IFrameOut frameOut)
	{
		frameOut = null;
		FrameOut frameOut2 = new FrameOut(_devHandle);
		MvCCDll.MV_FRAME_OUT pFrame = default(MvCCDll.MV_FRAME_OUT);
		int num = MvCCDll.MV_CC_GetImageBuffer(_devHandle, ref pFrame, (int)timeoutInMS);
		if (num != 0)
		{
			return num;
		}
		num = ConvertMvFrameOut2FrameOut(ref pFrame, ref frameOut2);
		if (num != 0)
		{
			MvCCDll.MV_CC_FreeImageBuffer(_devHandle, ref pFrame);
			return num;
		}
		frameOut2.NeedFree = true;
		frameOut = frameOut2;
		return num;
	}

	public int FreeImageBuffer(IFrameOut frame)
	{
		if (frame == null)
		{
			return -**********;
		}
		int num = 0;
		num = InnerTools.ConvertFrameOut2MvFrameOut(frame, out var mvFrameOut);
		if (num != 0)
		{
			return num;
		}
		num = MvCCDll.MV_CC_FreeImageBuffer(_devHandle, ref mvFrameOut);
		if (num != 0)
		{
			return num;
		}
		(frame as FrameOut).NeedFree = false;
		return num;
	}

	public int ClearImageBuffer()
	{
		return MvCCDll.MV_CC_ClearImageBuffer(_devHandle);
	}

	/// <summary>
	/// 内部回调图像回调函数
	/// </summary>
	/// <param name="pData"></param>
	/// <param name="pFrameInfo"></param>
	/// <param name="pUser"></param>
	private void OutputCallback(IntPtr pData, ref MvCCDll.MV_FRAME_OUT_INFO_EX pFrameInfo, IntPtr pUser)
	{
		FrameOut frameOut = new FrameOut(_devHandle);
		MvCCDll.MV_FRAME_OUT mvFrameOut = new MvCCDll.MV_FRAME_OUT
		{
			stFrameInfo = pFrameInfo,
			pBufAddr = pData
		};
		if (ConvertMvFrameOut2FrameOut(ref mvFrameOut, ref frameOut) == 0 && this._FrameGrabedEvent != null)
		{
			this._FrameGrabedEvent(this, new FrameGrabbedEventArgs(frameOut));
		}
	}

	/// <summary>
	/// 内部流异常回调函数
	/// </summary>
	/// <param name="enExceptionType"></param>
	/// <param name="pUser"></param>
	private void StreamExceptionCallback(MvCCDll.MV_CC_STREAM_EXCEPTION_TYPE enExceptionType, IntPtr pUser)
	{
		if (this._StreamExceptionEvent != null)
		{
			this._StreamExceptionEvent(this, new StreamExceptionEventArgs((StreamExceptionType)enExceptionType));
		}
	}

	/// <summary>
	/// 将MvCCDll中的帧结构体转为FrameOut类
	/// </summary>
	/// <param name="mvFrameOut"></param>
	/// <param name="frameOut"></param>
	/// <returns></returns>
	public int ConvertMvFrameOut2FrameOut(ref MvCCDll.MV_FRAME_OUT mvFrameOut, ref FrameOut frameOut)
	{
		if (frameOut == null)
		{
			return -**********;
		}
		MvNativeImage image = new MvNativeImage(mvFrameOut.stFrameInfo.nExtendWidth, mvFrameOut.stFrameInfo.nExtendHeight, (MvGvspPixelType)mvFrameOut.stFrameInfo.enPixelType, mvFrameOut.pBufAddr, mvFrameOut.stFrameInfo.nFrameLenEx, _devHandle);
		frameOut.Image = image;
		frameOut.FrameNum = mvFrameOut.stFrameInfo.nFrameNum;
		frameOut.DevTimeStamp = ((ulong)mvFrameOut.stFrameInfo.nDevTimeStampHigh << 32) | mvFrameOut.stFrameInfo.nDevTimeStampLow;
		frameOut.HostTimeStamp = (ulong)mvFrameOut.stFrameInfo.nHostTimeStamp;
		frameOut.FrameLen = mvFrameOut.stFrameInfo.nFrameLenEx;
		frameOut.SecondCount = mvFrameOut.stFrameInfo.nSecondCount;
		frameOut.CycleCount = mvFrameOut.stFrameInfo.nCycleCount;
		frameOut.CycleOffset = mvFrameOut.stFrameInfo.nCycleOffset;
		frameOut.Gain = mvFrameOut.stFrameInfo.fGain;
		frameOut.ExposureTime = mvFrameOut.stFrameInfo.fExposureTime;
		frameOut.AverageBrightness = mvFrameOut.stFrameInfo.nAverageBrightness;
		frameOut.Red = mvFrameOut.stFrameInfo.nRed;
		frameOut.Green = mvFrameOut.stFrameInfo.nGreen;
		frameOut.Blue = mvFrameOut.stFrameInfo.nBlue;
		frameOut.FrameCount = mvFrameOut.stFrameInfo.nFrameCounter;
		frameOut.TriggerIndex = mvFrameOut.stFrameInfo.nTriggerIndex;
		frameOut.Input = mvFrameOut.stFrameInfo.nInput;
		frameOut.Output = mvFrameOut.stFrameInfo.nOutput;
		frameOut.OffsetX = mvFrameOut.stFrameInfo.nOffsetX;
		frameOut.OffsetY = mvFrameOut.stFrameInfo.nOffsetY;
		frameOut.LostPacket = mvFrameOut.stFrameInfo.nLostPacket;
		ChunkInfo chunkInfo = new ChunkInfo();
		int num = Marshal.SizeOf((object)default(MvCCDll.MV_CHUNK_DATA_CONTENT));
		for (int i = 0; i < mvFrameOut.stFrameInfo.nUnparsedChunkNum; i++)
		{
			MvCCDll.MV_CHUNK_DATA_CONTENT mV_CHUNK_DATA_CONTENT = (MvCCDll.MV_CHUNK_DATA_CONTENT)Marshal.PtrToStructure(mvFrameOut.stFrameInfo.UnparsedChunkList.pUnparsedChunkContent + i * num, typeof(MvCCDll.MV_CHUNK_DATA_CONTENT));
			ChunkData chunkData = new ChunkData();
			chunkData.ChunkID = mV_CHUNK_DATA_CONTENT.nChunkID;
			chunkData.DataPtr = mV_CHUNK_DATA_CONTENT.pChunkData;
			chunkData.Length = mV_CHUNK_DATA_CONTENT.nChunkLen;
			chunkInfo[chunkData.ChunkID] = chunkData;
		}
		frameOut.ChunkInfo = chunkInfo;
		return 0;
	}
}
