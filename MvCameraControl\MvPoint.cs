namespace MvCameraControl;

/// <summary>
/// 点
/// </summary>
public struct MvPoint
{
	/// <summary>
	/// 该点距离图像左边缘距离，根据图像的相对位置，范围为[0.0 , 1.0]
	/// </summary>
	public float X;

	/// <summary>
	/// 该点距离图像上边缘距离，根据图像的相对位置，范围为[0.0 , 1.0]
	/// </summary>
	public float Y;

	/// <summary>
	/// 构造函数
	/// </summary>
	/// <param name="x">该点距离图像左边缘距离，根据图像的相对位置，范围为[0.0 , 1.0]</param>
	/// <param name="y">该点距离图像上边缘距离，根据图像的相对位置，范围为[0.0 , 1.0]</param>
	public MvPoint(float x, float y)
	{
		X = x;
		Y = y;
	}
}
