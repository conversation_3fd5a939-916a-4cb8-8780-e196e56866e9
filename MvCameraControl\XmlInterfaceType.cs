namespace MvCameraControl;

/// <summary>
/// 每个节点对应的接口类型
/// </summary>
public enum XmlInterfaceType
{
	/// <summary>
	/// Value 
	/// </summary>
	IValue,
	/// <summary>
	/// Base 
	/// </summary>
	IBase,
	/// <summary>
	/// Integer 
	/// </summary>
	IInteger,
	/// <summary>
	/// Boolean 
	/// </summary>
	IBoolean,
	/// <summary>
	/// Command
	/// </summary>
	ICommand,
	/// <summary>
	///  Float 
	/// </summary>
	IFloat,
	/// <summary>
	/// String  
	/// </summary>
	IString,
	/// <summary>
	/// Register 
	/// </summary>
	IRegister,
	/// <summary>
	/// Category 
	/// </summary>
	ICategory,
	/// <summary>
	/// Enumeration
	/// </summary>
	IEnumeration,
	/// <summary>
	/// EnumEntry 
	/// </summary>
	IEnumEntry,
	/// <summary>
	/// Port 
	/// </summary>
	IPort
}
