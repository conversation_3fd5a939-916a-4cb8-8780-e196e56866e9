using System;
using System.Runtime.InteropServices;

namespace MvCameraControl;

internal class ImageSaver : IImageSaver
{
	private IntPtr _devHandle = IntPtr.Zero;

	internal ImageSaver(IntPtr devHandle)
	{
		_devHandle = devHandle;
	}

	public int SaveImageToBuffer(byte[] buffer, out uint dataLen, IImage image, ImageFormatInfo imageFormatInfo, CFAMethod cfaMethod)
	{
		dataLen = 0u;
		if (image == null)
		{
			return -**********;
		}
		int num = 0;
		MvCCDll.MV_SAVE_IMAGE_PARAM_EX3 stSaveParam = new MvCCDll.MV_SAVE_IMAGE_PARAM_EX3
		{
			nWidth = image.Width,
			nHeight = image.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)image.PixelType,
			pData = image.PixelDataPtr,
			nDataLen = (uint)image.ImageSize
		};
		if (buffer == null)
		{
			stSaveParam.pImageBuffer = IntPtr.Zero;
			stSaveParam.nBufferSize = 0u;
		}
		else
		{
			stSaveParam.pImageBuffer = Marshal.UnsafeAddrOfPinnedArrayElement((Array)buffer, 0);
			stSaveParam.nBufferSize = Convert.ToUInt32(buffer.Length);
		}
		stSaveParam.enImageType = (MvCCDll.MV_SAVE_IAMGE_TYPE)imageFormatInfo.FormatType;
		stSaveParam.nJpgQuality = imageFormatInfo.JpegQuality;
		stSaveParam.iMethodValue = (uint)cfaMethod;
		num = MvCCDll.MV_CC_SaveImageEx3(_devHandle, ref stSaveParam);
		dataLen = stSaveParam.nImageLen;
		return num;
	}

	public int SaveImageToBuffer(byte[] dstBuffer, out ulong dataLen, IntPtr srcBuffer, ImageInfo imageInfo, ImageFormatInfo imageFormatInfo, CFAMethod cfaMethod)
	{
		dataLen = 0uL;
		if (srcBuffer == IntPtr.Zero || imageInfo == null)
		{
			return -**********;
		}
		int num = 0;
		MvCCDll.MV_SAVE_IMAGE_PARAM_EX3 stSaveParam = new MvCCDll.MV_SAVE_IMAGE_PARAM_EX3
		{
			nWidth = imageInfo.Width,
			nHeight = imageInfo.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)imageInfo.PixelType,
			pData = srcBuffer,
			nDataLen = (uint)imageInfo.ImageSize
		};
		if (dstBuffer == null)
		{
			stSaveParam.pImageBuffer = IntPtr.Zero;
			stSaveParam.nBufferSize = 0u;
		}
		else
		{
			stSaveParam.pImageBuffer = Marshal.UnsafeAddrOfPinnedArrayElement((Array)dstBuffer, 0);
			stSaveParam.nBufferSize = Convert.ToUInt32(dstBuffer.LongLength);
		}
		stSaveParam.enImageType = (MvCCDll.MV_SAVE_IAMGE_TYPE)imageFormatInfo.FormatType;
		stSaveParam.nJpgQuality = imageFormatInfo.JpegQuality;
		stSaveParam.iMethodValue = (uint)cfaMethod;
		num = MvCCDll.MV_CC_SaveImageEx3(_devHandle, ref stSaveParam);
		dataLen = stSaveParam.nImageLen;
		return num;
	}

	public int SaveImageToFile(string filePath, IImage image, ImageFormatInfo imageFormatInfo, CFAMethod cfaMethod)
	{
		if (image == null)
		{
			return -**********;
		}
		int num = 0;
		MvCCDll.MV_CC_IMAGE pstImage = default(MvCCDll.MV_CC_IMAGE);
		MvCCDll.MV_CC_SAVE_IMAGE_PARAM pSaveImageParam = default(MvCCDll.MV_CC_SAVE_IMAGE_PARAM);
		pstImage.nWidth = image.Width;
		pstImage.nHeight = image.Height;
		pstImage.enPixelType = (MvCCDll.MvGvspPixelType)image.PixelType;
		pstImage.pImageBuf = image.PixelDataPtr;
		pstImage.nImageLen = image.ImageSize;
		pSaveImageParam.enImageType = (MvCCDll.MV_SAVE_IAMGE_TYPE)imageFormatInfo.FormatType;
		pSaveImageParam.nQuality = imageFormatInfo.JpegQuality;
		pSaveImageParam.iMethodValue = (uint)cfaMethod;
		return MvCCDll.MV_CC_SaveImageToFileEx2(_devHandle, ref pstImage, ref pSaveImageParam, filePath);
	}

	public int SaveImageToFile(string filePath, IntPtr srcBuffer, ImageInfo imageInfo, ImageFormatInfo imageFormatInfo, CFAMethod cfaMethod)
	{
		if (srcBuffer == IntPtr.Zero || imageInfo == null)
		{
			return -**********;
		}
		int num = 0;
		MvCCDll.MV_CC_IMAGE pstImage = default(MvCCDll.MV_CC_IMAGE);
		MvCCDll.MV_CC_SAVE_IMAGE_PARAM pSaveImageParam = default(MvCCDll.MV_CC_SAVE_IMAGE_PARAM);
		pstImage.nWidth = imageInfo.Width;
		pstImage.nHeight = imageInfo.Height;
		pstImage.enPixelType = (MvCCDll.MvGvspPixelType)imageInfo.PixelType;
		pstImage.pImageBuf = srcBuffer;
		pstImage.nImageLen = imageInfo.ImageSize;
		pSaveImageParam.enImageType = (MvCCDll.MV_SAVE_IAMGE_TYPE)imageFormatInfo.FormatType;
		pSaveImageParam.nQuality = imageFormatInfo.JpegQuality;
		pSaveImageParam.iMethodValue = (uint)cfaMethod;
		return MvCCDll.MV_CC_SaveImageToFileEx2(_devHandle, ref pstImage, ref pSaveImageParam, filePath);
	}
}
