namespace MvCameraControl;

/// <summary>
/// 像素格式
/// </summary>
public enum MvGvspPixelType
{
	/// <summary>
	/// 未定义像素格式
	/// </summary>
	PixelType_Gvsp_Undefined = -1,
	/// <summary>
	/// Mono1p
	/// </summary>
	PixelType_Gvsp_Mono1p = 16842807,
	/// <summary>
	/// Mono2p
	/// </summary>
	PixelType_Gvsp_Mono2p = 16908344,
	/// <summary>
	/// Mono4p
	/// </summary>
	PixelType_Gvsp_Mono4p = 17039417,
	/// <summary>
	/// Mono8
	/// </summary>
	PixelType_Gvsp_Mono8 = 17301505,
	/// <summary>
	/// Mono8_Signed
	/// </summary>
	PixelType_Gvsp_Mono8_Signed = 17301506,
	/// <summary>
	/// Mono10
	/// </summary>
	PixelType_Gvsp_Mono10 = 17825795,
	/// <summary>
	/// Mono10_Packed
	/// </summary>
	PixelType_Gvsp_Mono10_Packed = 17563652,
	/// <summary>
	/// Mono12
	/// </summary>
	PixelType_Gvsp_Mono12 = 17825797,
	/// <summary>
	/// Mono12_Packed
	/// </summary>
	PixelType_Gvsp_Mono12_Packed = 17563654,
	/// <summary>
	/// Mono14
	/// </summary>
	PixelType_Gvsp_Mono14 = 17825829,
	/// <summary>
	/// Mono16
	/// </summary>
	PixelType_Gvsp_Mono16 = 17825799,
	/// <summary>
	/// BayerGR8
	/// </summary>
	PixelType_Gvsp_BayerGR8 = 17301512,
	/// <summary>
	/// BayerRG8
	/// </summary>
	PixelType_Gvsp_BayerRG8 = 17301513,
	/// <summary>
	/// BayerGB8
	/// </summary>
	PixelType_Gvsp_BayerGB8 = 17301514,
	/// <summary>
	/// BayerBG8
	/// </summary>
	PixelType_Gvsp_BayerBG8 = 17301515,
	/// <summary>
	/// BayerRBGG8
	/// </summary>
	PixelType_Gvsp_BayerRBGG8 = 17301574,
	/// <summary>
	/// BayerGR10
	/// </summary>
	PixelType_Gvsp_BayerGR10 = 17825804,
	/// <summary>
	/// BayerRG10
	/// </summary>
	PixelType_Gvsp_BayerRG10 = 17825805,
	/// <summary>
	/// BayerGB10
	/// </summary>
	PixelType_Gvsp_BayerGB10 = 17825806,
	/// <summary>
	/// BayerBG10
	/// </summary>
	PixelType_Gvsp_BayerBG10 = 17825807,
	/// <summary>
	/// BayerGR12
	/// </summary>
	PixelType_Gvsp_BayerGR12 = 17825808,
	/// <summary>
	/// BayerRG12
	/// </summary>
	PixelType_Gvsp_BayerRG12 = 17825809,
	/// <summary>
	/// BayerGB12
	/// </summary>
	PixelType_Gvsp_BayerGB12 = 17825810,
	/// <summary>
	/// BayerBG12
	/// </summary>
	PixelType_Gvsp_BayerBG12 = 17825811,
	/// <summary>
	/// BayerGR10_Packed
	/// </summary>
	PixelType_Gvsp_BayerGR10_Packed = 17563686,
	/// <summary>
	/// BayerRG10_Packed
	/// </summary>
	PixelType_Gvsp_BayerRG10_Packed = 17563687,
	/// <summary>
	/// BayerGB10_Packed
	/// </summary>
	PixelType_Gvsp_BayerGB10_Packed = 17563688,
	/// <summary>
	/// BayerBG10_Packed
	/// </summary>
	PixelType_Gvsp_BayerBG10_Packed = 17563689,
	/// <summary>
	/// BayerGR12_Packed
	/// </summary>
	PixelType_Gvsp_BayerGR12_Packed = 17563690,
	/// <summary>BayerRG12_Packed</summary>
	PixelType_Gvsp_BayerRG12_Packed = 17563691,
	/// <summary>BayerGB12_Packed</summary>
	PixelType_Gvsp_BayerGB12_Packed = 17563692,
	/// <summary>BayerBG12_Packed</summary>
	PixelType_Gvsp_BayerBG12_Packed = 17563693,
	/// <summary>BayerGR16</summary>
	PixelType_Gvsp_BayerGR16 = 17825838,
	/// <summary>BayerRG16</summary>
	PixelType_Gvsp_BayerRG16 = 17825839,
	/// <summary>BayerGB16</summary>
	PixelType_Gvsp_BayerGB16 = 17825840,
	/// <summary>BayerBG16</summary>
	PixelType_Gvsp_BayerBG16 = 17825841,
	/// <summary>RGB8_Packed</summary>
	PixelType_Gvsp_RGB8_Packed = 35127316,
	/// <summary>BGR8_Packed</summary>
	PixelType_Gvsp_BGR8_Packed = 35127317,
	/// <summary>RGBA8_Packed</summary>
	PixelType_Gvsp_RGBA8_Packed = 35651606,
	/// <summary>BGRA8_Packed</summary>
	PixelType_Gvsp_BGRA8_Packed = 35651607,
	/// <summary>RGB10_Packed</summary>
	PixelType_Gvsp_RGB10_Packed = 36700184,
	/// <summary>BGR10_Packed</summary>
	PixelType_Gvsp_BGR10_Packed = 36700185,
	/// <summary>RGB12_Packed</summary>
	PixelType_Gvsp_RGB12_Packed = 36700186,
	/// <summary>BGR12_Packed</summary>
	PixelType_Gvsp_BGR12_Packed = 36700187,
	/// <summary>RGB16_Packed</summary>
	PixelType_Gvsp_RGB16_Packed = 36700211,
	/// <summary>BGR16_Packed/// </summary>
	PixelType_Gvsp_BGR16_Packed = 36700235,
	/// <summary>RGBA16_Packed</summary>
	PixelType_Gvsp_RGBA16_Packed = 37748800,
	/// <summary>BGRA16_Packed</summary>
	PixelType_Gvsp_BGRA16_Packed = 37748817,
	/// <summary>RGB10V1_Packe</summary>
	PixelType_Gvsp_RGB10V1_Packed = 35651612,
	/// <summary>RGB10V2_Packed</summary>
	PixelType_Gvsp_RGB10V2_Packed = 35651613,
	/// <summary>RGB12V1_Packed</summary>
	PixelType_Gvsp_RGB12V1_Packed = 35913780,
	/// <summary>RGB565_Packed</summary>
	PixelType_Gvsp_RGB565_Packed = 34603061,
	/// <summary>BGR565_Packed</summary>
	PixelType_Gvsp_BGR565_Packed = 34603062,
	/// <summary>YUV411_Packed</summary>
	PixelType_Gvsp_YUV411_Packed = 34340894,
	/// <summary>YUV422_Packed</summary>
	PixelType_Gvsp_YUV422_Packed = 34603039,
	/// <summary>YUV422_YUYV_Packed</summary>
	PixelType_Gvsp_YUV422_YUYV_Packed = 34603058,
	/// <summary>YUV444_Packed</summary>
	PixelType_Gvsp_YUV444_Packed = 35127328,
	/// <summary>YCBCR8_CBYCR</summary>
	PixelType_Gvsp_YCBCR8_CBYCR = 35127354,
	/// <summary>YCBCR422_8</summary>
	PixelType_Gvsp_YCBCR422_8 = 34603067,
	/// <summary>YCBCR422_8_CBYCRY</summary>
	PixelType_Gvsp_YCBCR422_8_CBYCRY = 34603075,
	/// <summary>YCBCR411_8_CBYYCRYY</summary>
	PixelType_Gvsp_YCBCR411_8_CBYYCRYY = 34340924,
	/// <summary>YCBCR601_8_CBYCR</summary>
	PixelType_Gvsp_YCBCR601_8_CBYCR = 35127357,
	/// <summary>YCBCR601_422_8</summary>
	PixelType_Gvsp_YCBCR601_422_8 = 34603070,
	/// <summary>YCBCR601_422_8_CBYCRY</summary>
	PixelType_Gvsp_YCBCR601_422_8_CBYCRY = 34603076,
	/// <summary>YCBCR601_411_8_CBYYCRYY</summary>
	PixelType_Gvsp_YCBCR601_411_8_CBYYCRYY = 34340927,
	/// <summary>YCBCR709_8_CBYCR</summary>
	PixelType_Gvsp_YCBCR709_8_CBYCR = 35127360,
	/// <summary>YCBCR709_422_8</summary>
	PixelType_Gvsp_YCBCR709_422_8 = 34603073,
	/// <summary>YCBCR709_422_8_CBYCRY</summary>
	PixelType_Gvsp_YCBCR709_422_8_CBYCRY = 34603077,
	/// <summary>YCBCR709_411_8_CBYYCRYY</summary>
	PixelType_Gvsp_YCBCR709_411_8_CBYYCRYY = 34340930,
	/// <summary>YUV420SP_NV12</summary>
	PixelType_Gvsp_YUV420SP_NV12 = 34373633,
	/// <summary>YUV420SP_NV21</summary>
	PixelType_Gvsp_YUV420SP_NV21 = 34373634,
	/// <summary>RGB8_Planar</summary>
	PixelType_Gvsp_RGB8_Planar = 35127329,
	/// <summary>RGB10_Planar</summary>
	PixelType_Gvsp_RGB10_Planar = 36700194,
	/// <summary>RGB12_Planar</summary>
	PixelType_Gvsp_RGB12_Planar = 36700195,
	/// <summary>RGB16_Planar</summary>
	PixelType_Gvsp_RGB16_Planar = 36700196,
	/// <summary>Jpeg</summary>
	PixelType_Gvsp_Jpeg = -2145910783,
	/// <summary>Coord3D_ABC32f</summary>
	PixelType_Gvsp_Coord3D_ABC32f = 39846080,
	/// <summary>Coord3D_ABC32f_Planar</summary>
	PixelType_Gvsp_Coord3D_ABC32f_Planar = 39846081,
	/// <summary>Coord3D_AC32f</summary>
	PixelType_Gvsp_Coord3D_AC32f = 37748930,
	/// <summary>COORD3D_DEPTH_PLUS_MASK</summary>
	PixelType_Gvsp_COORD3D_DEPTH_PLUS_MASK = -2112094207,
	/// <summary>Coord3D_ABC32</summary>
	PixelType_Gvsp_Coord3D_ABC32 = -2107625471,
	/// <summary>Coord3D_AB32f</summary>
	PixelType_Gvsp_Coord3D_AB32f = -2109722622,
	/// <summary>Coord3D_AB32</summary>
	PixelType_Gvsp_Coord3D_AB32 = -2109722621,
	/// <summary>Coord3D_AC32f_64</summary>
	PixelType_Gvsp_Coord3D_AC32f_64 = 37748930,
	/// <summary>Coord3D_AC32f_Planar</summary>
	PixelType_Gvsp_Coord3D_AC32f_Planar = 37748931,
	/// <summary>Coord3D_AC32</summary>
	PixelType_Gvsp_Coord3D_AC32 = -2109722620,
	/// <summary>Coord3D_A32f</summary>
	PixelType_Gvsp_Coord3D_A32f = 18874557,
	/// <summary>Coord3D_A32</summary>
	PixelType_Gvsp_Coord3D_A32 = -2128596987,
	/// <summary>Coord3D_C32f</summary>
	PixelType_Gvsp_Coord3D_C32f = 18874559,
	/// <summary>Coord3D_C32</summary>
	PixelType_Gvsp_Coord3D_C32 = -2128596986,
	/// <summary>Coord3D_ABC16</summary>
	PixelType_Gvsp_Coord3D_ABC16 = 36700345,
	/// <summary>Coord3D_C16</summary>
	PixelType_Gvsp_Coord3D_C16 = 17825976,
	/// <summary>Float32</summary>
	PixelType_Gvsp_Float32 = -2128609279,
	/// <summary>HB_Mono8</summary>
	PixelType_Gvsp_HB_Mono8 = -2130182143,
	/// <summary>HB_Mono10</summary>
	PixelType_Gvsp_HB_Mono10 = -2129657853,
	/// <summary>HB_Mono10_Packed</summary>
	PixelType_Gvsp_HB_Mono10_Packed = -2129919996,
	/// <summary>HB_Mono12</summary>
	PixelType_Gvsp_HB_Mono12 = -2129657851,
	/// <summary>HB_Mono12_Packed</summary>
	PixelType_Gvsp_HB_Mono12_Packed = -2129919994,
	/// <summary>HB_Mono16</summary>
	PixelType_Gvsp_HB_Mono16 = -2129657849,
	/// <summary>HB_BayerGR8</summary>
	PixelType_Gvsp_HB_BayerGR8 = -2130182136,
	/// <summary>HB_BayerRG8</summary>
	PixelType_Gvsp_HB_BayerRG8 = -2130182135,
	/// <summary>HB_BayerGB8</summary>
	PixelType_Gvsp_HB_BayerGB8 = -2130182134,
	/// <summary>HB_BayerBG8</summary>
	PixelType_Gvsp_HB_BayerBG8 = -2130182133,
	/// <summary>HB_BayerRBGG8</summary>
	PixelType_Gvsp_HB_BayerRBGG8 = -2130182074,
	/// <summary>HB_BayerGR10</summary>
	PixelType_Gvsp_HB_BayerGR10 = -2129657844,
	/// <summary>HB_BayerRG10</summary>
	PixelType_Gvsp_HB_BayerRG10 = -2129657843,
	/// <summary>HB_BayerGB10</summary>
	PixelType_Gvsp_HB_BayerGB10 = -2129657842,
	/// <summary>HB_BayerBG10</summary>
	PixelType_Gvsp_HB_BayerBG10 = -2129657841,
	/// <summary>HB_BayerGR12</summary>
	PixelType_Gvsp_HB_BayerGR12 = -2129657840,
	/// <summary>HB_BayerRG12</summary>
	PixelType_Gvsp_HB_BayerRG12 = -2129657839,
	/// <summary>HB_BayerGB12</summary>
	PixelType_Gvsp_HB_BayerGB12 = -2129657838,
	/// <summary>HB_BayerBG12</summary>
	PixelType_Gvsp_HB_BayerBG12 = -2129657837,
	/// <summary>HB_BayerGR10_Packed</summary>
	PixelType_Gvsp_HB_BayerGR10_Packed = -2129919962,
	/// <summary>HB_BayerRG10_Packed</summary>
	PixelType_Gvsp_HB_BayerRG10_Packed = -2129919961,
	/// <summary>HB_BayerGB10_Packed</summary>
	PixelType_Gvsp_HB_BayerGB10_Packed = -2129919960,
	/// <summary>HB_BayerBG10_Packed</summary>
	PixelType_Gvsp_HB_BayerBG10_Packed = -2129919959,
	/// <summary>HB_BayerGR12_Packed</summary>
	PixelType_Gvsp_HB_BayerGR12_Packed = -2129919958,
	/// <summary>HB_BayerRG12_Packed</summary>
	PixelType_Gvsp_HB_BayerRG12_Packed = -2129919957,
	/// <summary>HB_BayerGB12_Packed</summary>
	PixelType_Gvsp_HB_BayerGB12_Packed = -2129919956,
	/// <summary>HB_BayerBG12_Packed</summary>
	PixelType_Gvsp_HB_BayerBG12_Packed = -2129919955,
	/// <summary>HB_YUV422_Packed</summary>
	PixelType_Gvsp_HB_YUV422_Packed = -2112880609,
	/// <summary>HB_YUV422_YUYV_Packed</summary>
	PixelType_Gvsp_HB_YUV422_YUYV_Packed = -2112880590,
	/// <summary>HB_RGB8_Packed</summary>
	PixelType_Gvsp_HB_RGB8_Packed = -2112356332,
	/// <summary>HB_BGR8_Packed</summary>
	PixelType_Gvsp_HB_BGR8_Packed = -2112356331,
	/// <summary>HB_RGBA8_Packed</summary>
	PixelType_Gvsp_HB_RGBA8_Packed = -2111832042,
	/// <summary>HB_BGRA8_Packed</summary>
	PixelType_Gvsp_HB_BGRA8_Packed = -2111832041,
	/// <summary>HB_RGB16_Packed</summary>
	PixelType_Gvsp_HB_RGB16_Packed = -2110783437,
	/// <summary>HB_BGR16_Packed</summary>
	PixelType_Gvsp_HB_BGR16_Packed = -2110783413,
	/// <summary>HB_RGBA16_Packed</summary>
	PixelType_Gvsp_HB_RGBA16_Packed = -2109734812,
	/// <summary>HB_BGRA16_Packed</summary>
	PixelType_Gvsp_HB_BGRA16_Packed = -2109734831
}
