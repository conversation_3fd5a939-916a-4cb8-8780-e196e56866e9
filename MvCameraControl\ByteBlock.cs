using System;
using System.Runtime.InteropServices;

namespace MvCameraControl;

/// <summary>
/// 内存块
/// </summary>
internal class ByteBlock
{
	private byte[] _byteArray;

	private int _blockFlag;

	private double _rentTime;

	private double _returnTime;

	public byte[] ByteArray => _byteArray;

	public IntPtr NativePtr => Marshal.UnsafeAddrOfPinnedArrayElement((Array)_byteArray, 0);

	public long Length => _byteArray.LongLength;

	public double RentTime
	{
		get
		{
			return _rentTime;
		}
		set
		{
			_rentTime = value;
		}
	}

	public double ReturnTime
	{
		get
		{
			return _returnTime;
		}
		set
		{
			_returnTime = value;
		}
	}

	public int Flag => _blockFlag;

	public ByteBlock(long size)
	{
		_byteArray = new byte[size];
	}

	public ByteBlock(long size, int flag)
	{
		_byteArray = new byte[size];
		_blockFlag = flag;
	}
}
