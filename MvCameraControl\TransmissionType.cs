namespace MvCameraControl;

/// <summary>
/// GigE传输类型
/// </summary>
public enum TransmissionType
{
	/// <summary>
	/// 单播 
	/// </summary>
	Unicast = 0,
	/// <summary>
	/// 组播 
	/// </summary>
	Multicast = 1,
	/// <summary>
	/// 局域网内广播 
	/// </summary>
	LimitedBroadcast = 2,
	/// <summary>
	/// 子网内广播 
	/// </summary>
	SubnetBroadcast = 3,
	/// <summary>
	/// 从相机获取
	/// </summary>
	CameraDefined = 4,
	/// <summary>
	/// 用户自定义应用端接收图像数据Port号
	/// </summary>
	UnicastDefinedPort = 5,
	/// <summary>
	/// 设置了单播，但本实例不接收图像数据 
	/// </summary>
	UnicastWithoutRecv = 65536,
	/// <summary>
	/// 组播模式，但本实例不接收图像数据 
	/// </summary>
	MulticastWithoutRecv = 65537
}
