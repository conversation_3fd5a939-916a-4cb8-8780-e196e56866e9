using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace MvCameraControl;

/// <summary>
///             提供枚举采集卡接口
/// </summary>
public static class InterfaceEnumerator
{
	/// <summary>
	/// 枚举采集卡 
	/// </summary>
	/// <param name="TLayerType">采集卡接口类型</param>
	/// <param name="interfaceInfoList">采集卡列表</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 枚举到采集卡后，通过<see cref="T:MvCameraControl.InterfaceFactory" />创建采集卡实例。
	/// </remarks>
	public static int EnumInterfaces(InterfaceTLayerType TLayerType, out List<IInterfaceInfo> interfaceInfoList)
	{
		return EnumInterfacesImpl(TLayerType, out interfaceInfoList);
	}

	private static int EnumInterfacesImpl(InterfaceTLayerType TLayerType, out List<IInterfaceInfo> interfaceInfoList)
	{
		interfaceInfoList = new List<IInterfaceInfo>();
		MvCCDll.MV_INTERFACE_INFO_LIST pInterfaceInfoList = default(MvCCDll.MV_INTERFACE_INFO_LIST);
		int num = MvCCDll.MV_CC_EnumInterfaces((uint)TLayerType, ref pInterfaceInfoList);
		if (num != 0)
		{
			return num;
		}
		for (int i = 0; i < pInterfaceInfoList.nInterfaceNum; i++)
		{
			MvCCDll.MV_INTERFACE_INFO mV_INTERFACE_INFO = (MvCCDll.MV_INTERFACE_INFO)Marshal.PtrToStructure(pInterfaceInfoList.pInterfaceInfo[i], typeof(MvCCDll.MV_INTERFACE_INFO));
			InterfaceInfo interfaceInfo = new InterfaceInfo();
			interfaceInfo.TLayerType = (InterfaceTLayerType)mV_INTERFACE_INFO.nTLayerType;
			interfaceInfo.PCIEInfo = mV_INTERFACE_INFO.nPCIEInfo;
			interfaceInfo.InterfaceID = mV_INTERFACE_INFO.chInterfaceID;
			interfaceInfo.DisplayName = mV_INTERFACE_INFO.chDisplayName;
			interfaceInfo.SerialNumber = mV_INTERFACE_INFO.chSerialNumber;
			interfaceInfo.ModelName = mV_INTERFACE_INFO.chModelName;
			interfaceInfo.Manufacturer = mV_INTERFACE_INFO.chManufacturer;
			interfaceInfo.DeviceVersion = mV_INTERFACE_INFO.chDeviceVersion;
			interfaceInfo.UserDefinedName = InnerTools.ByteArrayToString(mV_INTERFACE_INFO.chUserDefinedName);
			interfaceInfoList.Add(interfaceInfo);
		}
		return 0;
	}
}
