namespace MvCameraControl;

/// <summary>
/// 流异常类型
/// </summary>
public enum StreamExceptionType
{
	/// <summary>
	/// 异常的图像，该帧被丢弃
	/// </summary>
	AbnormalImage = 16385,
	/// <summary>
	/// 缓存列表溢出，清除最旧的一帧
	/// </summary>
	ListOverflow,
	/// <summary>
	/// 缓存列表为空，该帧被丢弃
	/// </summary>
	ListEmpty,
	/// <summary>
	/// 断流恢复
	/// </summary>
	Reconnection,
	/// <summary>
	/// 断流,恢复失败,取流被中止
	/// </summary>
	Disconnection,
	/// <summary>
	/// 设备异常,取流被中止
	/// </summary>
	DeviceException
}
