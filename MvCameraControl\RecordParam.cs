namespace MvCameraControl;

/// <summary>
/// 录像参数
/// </summary>
public struct RecordParam
{
	/// <summary>
	/// 输入数据的像素格式
	/// </summary>
	public MvGvspPixelType PixelType;

	/// <summary>
	/// 图像宽（指定目标参数时需为2的倍数）
	/// </summary>
	public uint Width;

	/// <summary>
	/// 图像高（指定目标参数时需为2的倍数）
	/// </summary>
	public uint Height;

	/// <summary>
	/// 帧率fps(大于1/16) 
	/// </summary>
	public float FrameRate;

	/// <summary>
	/// 码率kbps(128-16*1024) 
	/// </summary>
	public uint BitRate;

	/// <summary>
	/// 录像格式
	/// </summary>
	public VideoFormatType FormatType;
}
