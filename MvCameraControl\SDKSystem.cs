namespace MvCameraControl;

/// <summary>
/// SDk全局信息及操作接口
/// </summary>
public static class SDKSystem
{
	private static string GetSDKVersionImpl()
	{
		uint num = MvCCDll.MV_CC_GetSDKVersion();
		return $"{num >> 24}.{(num >> 16) & 0xFF}.{(num >> 8) & 0xFF}.{num & 0xFF}";
	}

	private static int InitializeImpl()
	{
		return MvCCDll.MV_CC_Initialize();
	}

	private static int FinalizeImpl()
	{
		return MvCCDll.MV_CC_Finalize();
	}

	/// <summary>
	/// 获取SDK版本信息
	/// </summary>
	/// <returns>SDK版本号，格式x.y.z.a</returns>
	public static string GetSDKVersion()
	{
		return GetSDKVersionImpl();
	}

	/// <summary>
	/// 初始化SDK
	/// </summary>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public static int Initialize()
	{
		return InitializeImpl();
	}

	/// <summary>
	/// 反初始化SDK，释放资源 
	/// </summary>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public static int Finalize()
	{
		return FinalizeImpl();
	}
}
