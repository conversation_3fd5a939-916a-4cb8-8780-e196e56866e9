using System.Text.RegularExpressions;

namespace MvCameraControl;

/// <summary>
/// 创建设备对象
/// </summary>
internal static class DeviceFactoryImpl
{
	/// <summary>
	/// 创建设备对象
	/// </summary>
	/// <param name="deviceInfo">设备信息</param>
	/// <returns>设备实例</returns>
	public static IDevice CreateDevice(IDeviceInfo deviceInfo)
	{
		if (deviceInfo == null)
		{
			throw new MvException(-2147483644, $"CreateDevice failed! ErrorCode:{-2147483644:x}");
		}
		Device device = null;
		if (DeviceTLayerType.MvGigEDevice == deviceInfo.TLayerType || DeviceTLayerType.MvGenTLGigEDevice == deviceInfo.TLayerType || DeviceTLayerType.MvVirGigEDevice == deviceInfo.TLayerType)
		{
			return new GigEDevice(deviceInfo);
		}
		if (DeviceTLayerType.MvUsbDevice == deviceInfo.TLayerType || DeviceTLayerType.MvVirUsbDevice == deviceInfo.TLayerType)
		{
			return new USBDevice(deviceInfo);
		}
		if (DeviceTLayerType.MvCameraLinkDevice == deviceInfo.TLayerType)
		{
			return new CamlDevice(deviceInfo);
		}
		return new Device(deviceInfo);
	}

	/// <summary>
	/// 通过设备IP地址创建设备，适用于GigE设备
	/// </summary>
	/// <param name="deviceIp">设备IP地址</param>
	/// <param name="netExportIp">网口IP地址</param>
	/// <returns>设备实例</returns>
	public static IDevice CreateDeviceByIp(string deviceIp, string netExportIp)
	{
		if (deviceIp == null || netExportIp == null || !IsValidIPv4(deviceIp) || !IsValidIPv4(netExportIp))
		{
			throw new MvException(-2147483644, $"CreateDeviceByIp failed! ErrorCode:{-2147483644:x}");
		}
		return CreateDevice(new GigEDeviceInfo
		{
			TLayerType = DeviceTLayerType.MvGigEDevice,
			CurrentIp = IpStringToUInt(deviceIp),
			NetExport = IpStringToUInt(netExportIp)
		});
	}

	/// <summary>
	/// 通过GenTL设备信息创建设备句柄 
	/// </summary>
	/// <param name="devInfo">设备信息</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public static IDevice CreateDeviceByGenTL(IGenTLDevInfo devInfo)
	{
		return new Device(devInfo);
	}

	private static bool IsValidIPv4(string ip)
	{
		string pattern = "^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
		return Regex.IsMatch(ip, pattern);
	}

	private static uint IpStringToUInt(string ip)
	{
		string[] array = ip.Split('.');
		if (array.Length != 4)
		{
			return 0u;
		}
		return (uint.Parse(array[0]) << 24) | (uint.Parse(array[1]) << 16) | (uint.Parse(array[2]) << 8) | uint.Parse(array[3]);
	}
}
