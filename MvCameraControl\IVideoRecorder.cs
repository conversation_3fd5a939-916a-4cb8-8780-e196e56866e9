namespace MvCameraControl;

/// <summary>
/// 录像,将图片录制成AVI格式视频
/// </summary>
public interface IVideoRecorder
{
	/// <summary>
	/// 开始录像
	/// </summary>
	/// <param name="filePath">录像文件存放路径</param>
	/// <param name="recordParam">录像参数</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	int StartRecord(string filePath, RecordParam recordParam);

	/// <summary>
	/// 输入录像数据 
	/// </summary>
	/// <param name="image">图像数据</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	int InputOneFrame(IImage image);

	/// <summary>
	/// 停止录像 
	/// </summary>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	int StopRecord();
}
