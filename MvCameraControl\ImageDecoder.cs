using System;
using System.Runtime.InteropServices;

namespace MvCameraControl;

/// <summary>
/// 解码功能实现类
/// </summary>
internal class ImageDecoder : IImageDecoder
{
	private IntPtr _devHandle;

	private ByteBlockPool _byteBlockPool;

	public ImageDecoder(IntPtr devHandle)
	{
		_devHandle = devHandle;
	}

	public ImageDecoder(IntPtr devHandle, ByteBlockPool pool)
	{
		_devHandle = devHandle;
		_byteBlockPool = pool;
	}

	public int HBDecode(IFrameOut inFrameInfo, out IFrameOut outFrameInfo)
	{
		outFrameInfo = null;
		if (inFrameInfo == null)
		{
			return -2147483644;
		}
		int num = 0;
		MvCCDll.MV_CC_HB_DECODE_PARAM pstDecodeParam = new MvCCDll.MV_CC_HB_DECODE_PARAM
		{
			pSrcBuf = inFrameInfo.Image.PixelDataPtr,
			nSrcLen = (uint)inFrameInfo.Image.ImageSize,
			nDstBufSize = 0u
		};
		num = MvCCDll.MV_CC_HB_Decode(_devHandle, ref pstDecodeParam);
		ByteBlock byteBlock = _byteBlockPool.GetByteBlock(pstDecodeParam.nDstBufLen);
		pstDecodeParam.pDstBuf = byteBlock.NativePtr;
		pstDecodeParam.nDstBufSize = pstDecodeParam.nDstBufLen;
		num = MvCCDll.MV_CC_HB_Decode(_devHandle, ref pstDecodeParam);
		if (num != 0)
		{
			_byteBlockPool.FreeByteBlock(byteBlock, isDispose: true);
			return num;
		}
		FrameOut frameOut = new FrameOut(_devHandle);
		MvPooledImage image = new MvPooledImage(pstDecodeParam.nWidth, pstDecodeParam.nHeight, (MvGvspPixelType)pstDecodeParam.enDstPixelType, pstDecodeParam.nDstBufLen, byteBlock, _devHandle, _byteBlockPool);
		frameOut.Image = image;
		frameOut.SecondCount = pstDecodeParam.stFrameSpecInfo.nSecondCount;
		frameOut.CycleCount = pstDecodeParam.stFrameSpecInfo.nCycleCount;
		frameOut.CycleOffset = pstDecodeParam.stFrameSpecInfo.nCycleOffset;
		frameOut.Gain = pstDecodeParam.stFrameSpecInfo.fGain;
		frameOut.ExposureTime = pstDecodeParam.stFrameSpecInfo.fExposureTime;
		frameOut.AverageBrightness = pstDecodeParam.stFrameSpecInfo.nAverageBrightness;
		frameOut.Red = pstDecodeParam.stFrameSpecInfo.nRed;
		frameOut.Green = pstDecodeParam.stFrameSpecInfo.nGreen;
		frameOut.Blue = pstDecodeParam.stFrameSpecInfo.nBlue;
		frameOut.FrameCount = pstDecodeParam.stFrameSpecInfo.nFrameCounter;
		frameOut.TriggerIndex = pstDecodeParam.stFrameSpecInfo.nTriggerIndex;
		frameOut.Input = pstDecodeParam.stFrameSpecInfo.nInput;
		frameOut.Output = pstDecodeParam.stFrameSpecInfo.nOutput;
		frameOut.OffsetX = pstDecodeParam.stFrameSpecInfo.nOffsetX;
		frameOut.OffsetY = pstDecodeParam.stFrameSpecInfo.nOffsetY;
		frameOut.FrameLen = pstDecodeParam.nDstBufLen;
		frameOut.FrameNum = inFrameInfo.FrameNum;
		frameOut.DevTimeStamp = inFrameInfo.DevTimeStamp;
		frameOut.HostTimeStamp = inFrameInfo.HostTimeStamp;
		outFrameInfo = frameOut;
		return num;
	}

	internal int HBDecode(IImage inImage, out IImage outImage)
	{
		outImage = null;
		int num = 0;
		MvCCDll.MV_CC_HB_DECODE_PARAM pstDecodeParam = new MvCCDll.MV_CC_HB_DECODE_PARAM
		{
			pSrcBuf = inImage.PixelDataPtr,
			nSrcLen = (uint)inImage.ImageSize,
			nDstBufSize = 0u
		};
		num = MvCCDll.MV_CC_HB_Decode(_devHandle, ref pstDecodeParam);
		byte[] array = new byte[pstDecodeParam.nDstBufLen];
		pstDecodeParam.pDstBuf = Marshal.UnsafeAddrOfPinnedArrayElement((Array)array, 0);
		pstDecodeParam.nDstBufSize = pstDecodeParam.nDstBufLen;
		num = MvCCDll.MV_CC_HB_Decode(_devHandle, ref pstDecodeParam);
		if (num != 0)
		{
			return num;
		}
		outImage = new MvImage(pstDecodeParam.nWidth, pstDecodeParam.nHeight, (MvGvspPixelType)pstDecodeParam.enDstPixelType, pstDecodeParam.nDstBufLen, array, _devHandle);
		return num;
	}

	/// <summary>
	/// HB解码, 输出IntPtr类型缓存信息
	/// </summary>
	/// <param name="inFrameInfo"></param>
	/// <param name="dstBuffer"></param>
	/// <param name="dstBufferSize"></param>
	/// <param name="imageInfo"></param>
	/// <param name="frameSpecInfo"></param>
	/// <returns></returns>
	public int HBDecode(IFrameOut inFrameInfo, IntPtr dstBuffer, ulong dstBufferSize, out ImageInfo imageInfo, out FrameSpecInfo frameSpecInfo)
	{
		imageInfo = new ImageInfo();
		frameSpecInfo = new FrameSpecInfo();
		if (inFrameInfo == null)
		{
			return -2147483644;
		}
		int num = 0;
		MvCCDll.MV_CC_HB_DECODE_PARAM pstDecodeParam = new MvCCDll.MV_CC_HB_DECODE_PARAM
		{
			pSrcBuf = inFrameInfo.Image.PixelDataPtr,
			nSrcLen = (uint)inFrameInfo.Image.ImageSize,
			pDstBuf = dstBuffer,
			nDstBufSize = (uint)dstBufferSize
		};
		num = MvCCDll.MV_CC_HB_Decode(_devHandle, ref pstDecodeParam);
		imageInfo.ImageSize = pstDecodeParam.nDstBufLen;
		imageInfo.Width = pstDecodeParam.nWidth;
		imageInfo.Height = pstDecodeParam.nHeight;
		imageInfo.PixelType = (MvGvspPixelType)pstDecodeParam.enDstPixelType;
		if (num != 0)
		{
			return num;
		}
		frameSpecInfo = new FrameSpecInfo();
		frameSpecInfo.SecondCount = pstDecodeParam.stFrameSpecInfo.nSecondCount;
		frameSpecInfo.CycleCount = pstDecodeParam.stFrameSpecInfo.nCycleCount;
		frameSpecInfo.CycleOffset = pstDecodeParam.stFrameSpecInfo.nCycleOffset;
		frameSpecInfo.Gain = pstDecodeParam.stFrameSpecInfo.fGain;
		frameSpecInfo.ExposureTime = pstDecodeParam.stFrameSpecInfo.fExposureTime;
		frameSpecInfo.AverageBrightness = pstDecodeParam.stFrameSpecInfo.nAverageBrightness;
		frameSpecInfo.Red = pstDecodeParam.stFrameSpecInfo.nRed;
		frameSpecInfo.Green = pstDecodeParam.stFrameSpecInfo.nGreen;
		frameSpecInfo.Blue = pstDecodeParam.stFrameSpecInfo.nBlue;
		frameSpecInfo.FrameCount = pstDecodeParam.stFrameSpecInfo.nFrameCounter;
		frameSpecInfo.TriggerIndex = pstDecodeParam.stFrameSpecInfo.nTriggerIndex;
		frameSpecInfo.Input = pstDecodeParam.stFrameSpecInfo.nInput;
		frameSpecInfo.Output = pstDecodeParam.stFrameSpecInfo.nOutput;
		frameSpecInfo.OffsetX = pstDecodeParam.stFrameSpecInfo.nOffsetX;
		frameSpecInfo.OffsetY = pstDecodeParam.stFrameSpecInfo.nOffsetY;
		return num;
	}
}
