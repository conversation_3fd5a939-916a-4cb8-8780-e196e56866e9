using System;

namespace MvCameraControl;

internal class FrameOut : IFrameOut, IDisposable, ICloneable
{
	private IImage _imageBuffer;

	private IntPtr _deviceHandle;

	private bool _isDisposed;

	/// <summary>
	/// 是否需要调用FreeImageBuffer释放缓存, 只有调用C接口获取的FrameOut才需要释放
	/// 该标志为用来避免缓存被多次释放或者提前释放
	/// </summary>
	internal bool NeedFree;

	/// <summary>
	/// 相机句柄
	/// </summary>
	internal IntPtr DeviceHandle => _deviceHandle;

	/// <summary>
	/// 图像类
	/// </summary>
	public IImage Image
	{
		get
		{
			return _imageBuffer;
		}
		set
		{
			_imageBuffer = value;
		}
	}

	public uint FrameNum { get; set; }

	public ulong DevTimeStamp { get; set; }

	public ulong HostTimeStamp { get; set; }

	public ulong FrameLen { get; set; }

	public uint SecondCount { get; set; }

	public uint CycleCount { get; set; }

	public uint CycleOffset { get; set; }

	public float Gain { get; set; }

	public float ExposureTime { get; set; }

	public uint AverageBrightness { get; set; }

	public uint Red { get; set; }

	public uint Green { get; set; }

	public uint Blue { get; set; }

	public uint FrameCount { get; set; }

	public uint TriggerIndex { get; set; }

	public uint Input { get; set; }

	public uint Output { get; set; }

	public uint OffsetX { get; set; }

	public uint OffsetY { get; set; }

	public uint LostPacket { get; set; }

	public IChunkInfo ChunkInfo { get; set; }

	public FrameOut(IntPtr devHandle)
	{
		_deviceHandle = devHandle;
	}

	~FrameOut()
	{
		Dispose(disposing: false);
	}

	public void Dispose()
	{
		Dispose(disposing: true);
		GC.SuppressFinalize(this);
	}

	protected virtual void Dispose(bool disposing)
	{
		if (!_isDisposed)
		{
			if (NeedFree)
			{
				InnerTools.ConvertFrameOut2MvFrameOut(this, out var mvFrameOut);
				MvCCDll.MV_CC_FreeImageBuffer(_deviceHandle, ref mvFrameOut);
			}
			if (_imageBuffer != null)
			{
				_imageBuffer.Dispose();
			}
			_isDisposed = true;
		}
	}

	/// <summary>
	/// 深拷贝
	/// </summary>
	/// <returns></returns>
	public object Clone()
	{
		FrameOut obj = MemberwiseClone() as FrameOut;
		obj.Image = Image.Clone() as IImage;
		return obj;
	}
}
