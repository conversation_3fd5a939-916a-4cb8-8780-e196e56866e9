using System;
using System.Runtime.InteropServices;

namespace MvCameraControl;

internal class ChunkData : IChunkData
{
	private byte[] _data;

	public IntPtr DataPtr { get; set; }

	public byte[] Data
	{
		get
		{
			_data = new byte[Length];
			Marshal.Copy(DataPtr, _data, 0, (int)Length);
			return _data;
		}
		protected set
		{
			Marshal.Copy(value, 0, DataPtr, Math.Min((int)Length, value.Length));
			_data = value;
		}
	}

	public uint ChunkID { get; set; }

	public uint Length { get; set; }
}
