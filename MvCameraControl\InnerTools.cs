using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Text;

namespace MvCameraControl;

internal static class InnerTools
{
	/// <summary>
	/// 判断像素是否为Mono格式
	/// </summary>
	/// <param name="pixelType"></param>
	/// <returns></returns>
	public static bool IsMonoPixel(MvGvspPixelType pixelType)
	{
		switch (pixelType)
		{
		case MvGvspPixelType.PixelType_Gvsp_Mono1p:
		case MvGvspPixelType.PixelType_Gvsp_Mono2p:
		case MvGvspPixelType.PixelType_Gvsp_Mono4p:
		case MvGvspPixelType.PixelType_Gvsp_Mono8:
		case MvGvspPixelType.PixelType_Gvsp_Mono8_Signed:
		case MvGvspPixelType.PixelType_Gvsp_Mono10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_Mono12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_Mono10:
		case MvGvspPixelType.PixelType_Gvsp_Mono12:
		case MvGvspPixelType.PixelType_Gvsp_Mono16:
		case MvGvspPixelType.PixelType_Gvsp_Mono14:
			return true;
		default:
			return false;
		}
	}

	/// <summary>
	/// 判断图像格式是否为彩色格式
	/// </summary>
	/// <param name="pixelType"></param>
	/// <returns></returns>
	public static bool IsColorPixel(MvGvspPixelType pixelType)
	{
		switch (pixelType)
		{
		case MvGvspPixelType.PixelType_Gvsp_BayerGR8:
		case MvGvspPixelType.PixelType_Gvsp_BayerRG8:
		case MvGvspPixelType.PixelType_Gvsp_BayerGB8:
		case MvGvspPixelType.PixelType_Gvsp_BayerBG8:
		case MvGvspPixelType.PixelType_Gvsp_BayerRBGG8:
		case MvGvspPixelType.PixelType_Gvsp_BayerGR10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerRG10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerGB10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerBG10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerGR12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerRG12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerGB12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerBG12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerGR10:
		case MvGvspPixelType.PixelType_Gvsp_BayerRG10:
		case MvGvspPixelType.PixelType_Gvsp_BayerGB10:
		case MvGvspPixelType.PixelType_Gvsp_BayerBG10:
		case MvGvspPixelType.PixelType_Gvsp_BayerGR12:
		case MvGvspPixelType.PixelType_Gvsp_BayerRG12:
		case MvGvspPixelType.PixelType_Gvsp_BayerGB12:
		case MvGvspPixelType.PixelType_Gvsp_BayerBG12:
		case MvGvspPixelType.PixelType_Gvsp_BayerGR16:
		case MvGvspPixelType.PixelType_Gvsp_BayerRG16:
		case MvGvspPixelType.PixelType_Gvsp_BayerGB16:
		case MvGvspPixelType.PixelType_Gvsp_BayerBG16:
		case MvGvspPixelType.PixelType_Gvsp_YUV422_Packed:
		case MvGvspPixelType.PixelType_Gvsp_YUV422_YUYV_Packed:
		case MvGvspPixelType.PixelType_Gvsp_RGB8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BGR8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_RGBA8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BGRA8_Packed:
			return true;
		default:
			return false;
		}
	}

	public static bool IsHBPixelType(MvGvspPixelType pixelType)
	{
		switch (pixelType)
		{
		case MvGvspPixelType.PixelType_Gvsp_HB_Mono8:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGR8:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerRG8:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGB8:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerBG8:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerRBGG8:
		case MvGvspPixelType.PixelType_Gvsp_HB_Mono10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_Mono12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGR10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerRG10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGB10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerBG10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGR12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerRG12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGB12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerBG12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_Mono10:
		case MvGvspPixelType.PixelType_Gvsp_HB_Mono12:
		case MvGvspPixelType.PixelType_Gvsp_HB_Mono16:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGR10:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerRG10:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGB10:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerBG10:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGR12:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerRG12:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGB12:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerBG12:
		case MvGvspPixelType.PixelType_Gvsp_HB_YUV422_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_YUV422_YUYV_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_RGB8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BGR8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_RGBA8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BGRA8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_RGB16_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BGR16_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BGRA16_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_RGBA16_Packed:
			return true;
		default:
			return false;
		}
	}

	/// <summary>
	/// 获取图像大小
	/// </summary>
	/// <param name="width">图像宽度</param>
	/// <param name="height">图像高度</param>
	/// <param name="pixelType">像素格式</param>
	/// <returns></returns>
	public static ulong GetImageSize(uint width, uint height, MvGvspPixelType pixelType)
	{
		ulong num = (ulong)width * (ulong)height;
		switch (pixelType)
		{
		case MvGvspPixelType.PixelType_Gvsp_HB_Mono8:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGR8:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerRG8:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGB8:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerBG8:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerRBGG8:
		case MvGvspPixelType.PixelType_Gvsp_Mono8:
		case MvGvspPixelType.PixelType_Gvsp_BayerGR8:
		case MvGvspPixelType.PixelType_Gvsp_BayerRG8:
		case MvGvspPixelType.PixelType_Gvsp_BayerGB8:
		case MvGvspPixelType.PixelType_Gvsp_BayerBG8:
		case MvGvspPixelType.PixelType_Gvsp_BayerRBGG8:
			return num;
		case MvGvspPixelType.PixelType_Gvsp_HB_Mono10:
		case MvGvspPixelType.PixelType_Gvsp_HB_Mono12:
		case MvGvspPixelType.PixelType_Gvsp_HB_Mono16:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGR10:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerRG10:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGB10:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerBG10:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGR12:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerRG12:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGB12:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerBG12:
		case MvGvspPixelType.PixelType_Gvsp_HB_YUV422_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_YUV422_YUYV_Packed:
		case MvGvspPixelType.PixelType_Gvsp_Mono10:
		case MvGvspPixelType.PixelType_Gvsp_Mono12:
		case MvGvspPixelType.PixelType_Gvsp_Mono16:
		case MvGvspPixelType.PixelType_Gvsp_BayerGR10:
		case MvGvspPixelType.PixelType_Gvsp_BayerRG10:
		case MvGvspPixelType.PixelType_Gvsp_BayerGB10:
		case MvGvspPixelType.PixelType_Gvsp_BayerBG10:
		case MvGvspPixelType.PixelType_Gvsp_BayerGR12:
		case MvGvspPixelType.PixelType_Gvsp_BayerRG12:
		case MvGvspPixelType.PixelType_Gvsp_BayerGB12:
		case MvGvspPixelType.PixelType_Gvsp_BayerBG12:
		case MvGvspPixelType.PixelType_Gvsp_BayerGR16:
		case MvGvspPixelType.PixelType_Gvsp_BayerRG16:
		case MvGvspPixelType.PixelType_Gvsp_BayerGB16:
		case MvGvspPixelType.PixelType_Gvsp_BayerBG16:
		case MvGvspPixelType.PixelType_Gvsp_YUV422_Packed:
		case MvGvspPixelType.PixelType_Gvsp_YUV422_YUYV_Packed:
			return num * 2;
		case MvGvspPixelType.PixelType_Gvsp_HB_Mono10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_Mono12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGR10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerRG10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGB10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerBG10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGR12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerRG12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerGB12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BayerBG12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_Mono10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_Mono12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerGR10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerRG10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerGB10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerBG10_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerGR12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerRG12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerGB12_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BayerBG12_Packed:
			return num * 3 / 2;
		case MvGvspPixelType.PixelType_Gvsp_HB_RGB8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BGR8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_RGB8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BGR8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_RGB8_Planar:
			return num * 3;
		case MvGvspPixelType.PixelType_Gvsp_HB_RGBA8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_BGRA8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_RGBA8_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BGRA8_Packed:
			return num * 4;
		case MvGvspPixelType.PixelType_Gvsp_Coord3D_A32:
		case MvGvspPixelType.PixelType_Gvsp_Coord3D_C32:
		case MvGvspPixelType.PixelType_Gvsp_Coord3D_A32f:
		case MvGvspPixelType.PixelType_Gvsp_Coord3D_C32f:
			return num * 4;
		case MvGvspPixelType.PixelType_Gvsp_HB_BGRA16_Packed:
		case MvGvspPixelType.PixelType_Gvsp_HB_RGBA16_Packed:
		case MvGvspPixelType.PixelType_Gvsp_Coord3D_AB32f:
		case MvGvspPixelType.PixelType_Gvsp_Coord3D_AB32:
		case MvGvspPixelType.PixelType_Gvsp_Coord3D_AC32:
		case MvGvspPixelType.PixelType_Gvsp_RGBA16_Packed:
		case MvGvspPixelType.PixelType_Gvsp_BGRA16_Packed:
		case MvGvspPixelType.PixelType_Gvsp_Coord3D_AC32f:
			return num * 2 * 4;
		case MvGvspPixelType.PixelType_Gvsp_Coord3D_ABC32:
		case MvGvspPixelType.PixelType_Gvsp_Coord3D_ABC32f:
			return num * 3 * 4;
		case MvGvspPixelType.PixelType_Gvsp_Coord3D_ABC16:
			return num * 3 * 2;
		default:
			return num * 3;
		}
	}

	/// <summary>
	/// 内存拷贝
	/// </summary>
	/// <param name="dest">目标缓存</param>
	/// <param name="src">源缓存</param>
	/// <param name="count">拷贝大小</param>
	[DllImport("kernel32.dll", EntryPoint = "RtlMoveMemory")]
	public static extern void CopyMemory(IntPtr dest, IntPtr src, uint count);

	/// <summary>
	/// 将FrameOut转成MvCCDll的帧结构体
	/// </summary>
	/// <param name="frameOut"></param>
	/// <param name="mvFrameOut"></param>
	/// <returns></returns>
	public static int ConvertFrameOut2MvFrameOut(IFrameOut frameOut, out MvCCDll.MV_FRAME_OUT mvFrameOut)
	{
		mvFrameOut = default(MvCCDll.MV_FRAME_OUT);
		if (frameOut == null)
		{
			return -**********;
		}
		mvFrameOut.pBufAddr = frameOut.Image.PixelDataPtr;
		mvFrameOut.stFrameInfo.enPixelType = (MvCCDll.MvGvspPixelType)frameOut.Image.PixelType;
		mvFrameOut.stFrameInfo.nHeight = (ushort)Math.Min(frameOut.Image.Height, 65535u);
		mvFrameOut.stFrameInfo.nWidth = (ushort)Math.Min(frameOut.Image.Width, 65535u);
		mvFrameOut.stFrameInfo.nFrameLen = (uint)frameOut.Image.ImageSize;
		mvFrameOut.stFrameInfo.nFrameLenEx = frameOut.Image.ImageSize;
		mvFrameOut.stFrameInfo.nExtendHeight = frameOut.Image.Height;
		mvFrameOut.stFrameInfo.nExtendWidth = frameOut.Image.Width;
		mvFrameOut.stFrameInfo.fExposureTime = frameOut.ExposureTime;
		mvFrameOut.stFrameInfo.fGain = frameOut.Gain;
		mvFrameOut.stFrameInfo.nAverageBrightness = frameOut.AverageBrightness;
		mvFrameOut.stFrameInfo.nBlue = frameOut.Blue;
		mvFrameOut.stFrameInfo.nSecondCount = frameOut.SecondCount;
		mvFrameOut.stFrameInfo.nCycleCount = frameOut.CycleCount;
		mvFrameOut.stFrameInfo.nCycleOffset = frameOut.CycleOffset;
		mvFrameOut.stFrameInfo.nDevTimeStampHigh = (uint)(frameOut.DevTimeStamp >> 32);
		mvFrameOut.stFrameInfo.nDevTimeStampLow = (uint)frameOut.DevTimeStamp;
		mvFrameOut.stFrameInfo.nFrameCounter = frameOut.FrameCount;
		mvFrameOut.stFrameInfo.nFrameNum = frameOut.FrameNum;
		mvFrameOut.stFrameInfo.nGreen = frameOut.Green;
		mvFrameOut.stFrameInfo.nHostTimeStamp = (long)frameOut.HostTimeStamp;
		mvFrameOut.stFrameInfo.nInput = frameOut.Input;
		mvFrameOut.stFrameInfo.nLostPacket = frameOut.LostPacket;
		mvFrameOut.stFrameInfo.nOffsetX = (ushort)frameOut.OffsetX;
		mvFrameOut.stFrameInfo.nOffsetY = (ushort)frameOut.OffsetY;
		mvFrameOut.stFrameInfo.nOutput = frameOut.Output;
		mvFrameOut.stFrameInfo.nRed = frameOut.Red;
		mvFrameOut.stFrameInfo.nTriggerIndex = frameOut.TriggerIndex;
		return 0;
	}

	/// <summary>
	/// 将Byte数组转为String，使用UTF-8编码，并去掉结尾的'\0'
	/// </summary>
	/// <param name="byteArray"></param>
	/// <returns></returns>
	public static string ByteArrayToString(byte[] byteArray)
	{
		try
		{
			if (IsTextUTF8(byteArray))
			{
				return Encoding.UTF8.GetString(byteArray).Split(default(char))[0];
			}
			return Encoding.Default.GetString(byteArray).Split(default(char))[0];
		}
		catch (Exception)
		{
			return "";
		}
	}

	/// <summary>
	/// String字符串拷贝到byte[]
	/// </summary>
	/// <param name="srcString"></param>
	/// <param name="dstByteArray"></param>
	public static void StringToByteArray(string srcString, byte[] dstByteArray)
	{
		byte[] bytes = Encoding.UTF8.GetBytes(srcString);
		long length = ((bytes.Length < dstByteArray.Length) ? bytes.Length : dstByteArray.Length);
		Array.Copy(bytes, dstByteArray, length);
	}

	/// <summary>
	/// 判断字符数组是否为utf-8
	/// </summary>
	/// <param name="inputStream">字符数组</param>
	/// <returns></returns>
	public static bool IsTextUTF8(byte[] inputStream)
	{
		int num = 0;
		bool flag = true;
		for (int i = 0; i < inputStream.Length; i++)
		{
			byte b = inputStream[i];
			if ((b & 0x80) == 128)
			{
				flag = false;
			}
			if (num == 0)
			{
				if ((b & 0x80) != 0)
				{
					if ((b & 0xC0) != 192)
					{
						return false;
					}
					num = 1;
					b <<= 2;
					while ((b & 0x80) == 128)
					{
						b <<= 1;
						num++;
					}
				}
			}
			else
			{
				if ((b & 0xC0) != 128)
				{
					return false;
				}
				num--;
			}
		}
		if (num != 0)
		{
			return false;
		}
		return !flag;
	}

	/// <summary>
	/// 将枚举出来的设备信息转化为输出格式
	/// </summary>
	/// <param name="stDevlist"></param>
	/// <param name="devInfoList"></param>
	/// <returns></returns>
	public static void DevInfoListStruct2DevInfoList(MvCCDll.MV_CC_DEVICE_INFO_LIST stDevlist, ref List<IDeviceInfo> devInfoList)
	{
		for (int i = 0; i < stDevlist.nDeviceNum; i++)
		{
			MvCCDll.MV_CC_DEVICE_INFO mV_CC_DEVICE_INFO = (MvCCDll.MV_CC_DEVICE_INFO)Marshal.PtrToStructure(stDevlist.pDeviceInfo[i], typeof(MvCCDll.MV_CC_DEVICE_INFO));
			if (1 == mV_CC_DEVICE_INFO.nTLayerType || 16 == mV_CC_DEVICE_INFO.nTLayerType || 64 == mV_CC_DEVICE_INFO.nTLayerType)
			{
				GigEDeviceInfo gigEDeviceInfo = new GigEDeviceInfo();
				gigEDeviceInfo.MajorVer = mV_CC_DEVICE_INFO.nMajorVer;
				gigEDeviceInfo.MinorVer = mV_CC_DEVICE_INFO.nMinorVer;
				gigEDeviceInfo.MacAddrHigh = mV_CC_DEVICE_INFO.nMacAddrHigh;
				gigEDeviceInfo.MacAddrLow = mV_CC_DEVICE_INFO.nMacAddrLow;
				gigEDeviceInfo.TLayerType = (DeviceTLayerType)mV_CC_DEVICE_INFO.nTLayerType;
				gigEDeviceInfo.DevTypeInfo = mV_CC_DEVICE_INFO.nDevTypeInfo;
				MvCCDll.MV_GIGE_DEVICE_INFO_EX mV_GIGE_DEVICE_INFO_EX = (MvCCDll.MV_GIGE_DEVICE_INFO_EX)MvCCDll.ByteToStruct(mV_CC_DEVICE_INFO.SpecialInfo.stGigEInfo, typeof(MvCCDll.MV_GIGE_DEVICE_INFO_EX));
				gigEDeviceInfo.ManufacturerName = mV_GIGE_DEVICE_INFO_EX.chManufacturerName;
				gigEDeviceInfo.ModelName = mV_GIGE_DEVICE_INFO_EX.chModelName;
				gigEDeviceInfo.DeviceVersion = mV_GIGE_DEVICE_INFO_EX.chDeviceVersion;
				gigEDeviceInfo.SerialNumber = mV_GIGE_DEVICE_INFO_EX.chSerialNumber;
				gigEDeviceInfo.UserDefinedName = ByteArrayToString(mV_GIGE_DEVICE_INFO_EX.chUserDefinedName);
				gigEDeviceInfo.IpCfgOption = mV_GIGE_DEVICE_INFO_EX.nIpCfgOption;
				gigEDeviceInfo.IpCfgCurrent = mV_GIGE_DEVICE_INFO_EX.nIpCfgCurrent;
				gigEDeviceInfo.CurrentIp = mV_GIGE_DEVICE_INFO_EX.nCurrentIp;
				gigEDeviceInfo.CurrentSubNetMask = mV_GIGE_DEVICE_INFO_EX.nCurrentSubNetMask;
				gigEDeviceInfo.DefultGateWay = mV_GIGE_DEVICE_INFO_EX.nDefultGateWay;
				gigEDeviceInfo.NetExport = mV_GIGE_DEVICE_INFO_EX.nNetExport;
				gigEDeviceInfo.VirtualDevice = false;
				gigEDeviceInfo.GenTLDevice = false;
				gigEDeviceInfo.HostIp = mV_GIGE_DEVICE_INFO_EX.nReserved[0];
				gigEDeviceInfo.nMulticastIp = mV_GIGE_DEVICE_INFO_EX.nReserved[2];
				gigEDeviceInfo.nMulticastPort = mV_GIGE_DEVICE_INFO_EX.nReserved[3];
				if (1 == mV_GIGE_DEVICE_INFO_EX.nReserved[1] || 16 == mV_CC_DEVICE_INFO.nTLayerType)
				{
					gigEDeviceInfo.VirtualDevice = true;
				}
				else if (2 == mV_GIGE_DEVICE_INFO_EX.nReserved[1] || 64 == mV_CC_DEVICE_INFO.nTLayerType)
				{
					gigEDeviceInfo.GenTLDevice = true;
				}
				devInfoList.Add(gigEDeviceInfo);
			}
			else if (4 == mV_CC_DEVICE_INFO.nTLayerType || 32 == mV_CC_DEVICE_INFO.nTLayerType)
			{
				USBDeviceInfo uSBDeviceInfo = new USBDeviceInfo();
				uSBDeviceInfo.TLayerType = (DeviceTLayerType)mV_CC_DEVICE_INFO.nTLayerType;
				uSBDeviceInfo.DevTypeInfo = mV_CC_DEVICE_INFO.nDevTypeInfo;
				MvCCDll.MV_USB3_DEVICE_INFO_EX mV_USB3_DEVICE_INFO_EX = (MvCCDll.MV_USB3_DEVICE_INFO_EX)MvCCDll.ByteToStruct(mV_CC_DEVICE_INFO.SpecialInfo.stUsb3VInfo, typeof(MvCCDll.MV_USB3_DEVICE_INFO_EX));
				uSBDeviceInfo.ManufacturerName = mV_USB3_DEVICE_INFO_EX.chManufacturerName;
				uSBDeviceInfo.ModelName = mV_USB3_DEVICE_INFO_EX.chModelName;
				uSBDeviceInfo.DeviceVersion = mV_USB3_DEVICE_INFO_EX.chDeviceVersion;
				uSBDeviceInfo.SerialNumber = mV_USB3_DEVICE_INFO_EX.chSerialNumber;
				uSBDeviceInfo.UserDefinedName = ByteArrayToString(mV_USB3_DEVICE_INFO_EX.chUserDefinedName);
				uSBDeviceInfo.CrtlInEndPoint = mV_USB3_DEVICE_INFO_EX.CrtlInEndPoint;
				uSBDeviceInfo.CrtlOutEndPoint = mV_USB3_DEVICE_INFO_EX.CrtlOutEndPoint;
				uSBDeviceInfo.StreamEndPoint = mV_USB3_DEVICE_INFO_EX.StreamEndPoint;
				uSBDeviceInfo.EventEndPoint = mV_USB3_DEVICE_INFO_EX.EventEndPoint;
				uSBDeviceInfo.VendorID = mV_USB3_DEVICE_INFO_EX.idVendor;
				uSBDeviceInfo.ProductID = mV_USB3_DEVICE_INFO_EX.idProduct;
				uSBDeviceInfo.DeviceNumber = mV_USB3_DEVICE_INFO_EX.nDeviceNumber;
				uSBDeviceInfo.DeviceGUID = mV_USB3_DEVICE_INFO_EX.chDeviceGUID;
				uSBDeviceInfo.FamilyName = mV_USB3_DEVICE_INFO_EX.chFamilyName;
				uSBDeviceInfo.nbcdUSB = mV_USB3_DEVICE_INFO_EX.nbcdUSB;
				uSBDeviceInfo.DeviceAddress = mV_USB3_DEVICE_INFO_EX.nDeviceAddress;
				uSBDeviceInfo.VirtualDevice = false;
				if (1 == mV_USB3_DEVICE_INFO_EX.nReserved[1] || 32 == mV_CC_DEVICE_INFO.nTLayerType)
				{
					uSBDeviceInfo.VirtualDevice = true;
				}
				devInfoList.Add(uSBDeviceInfo);
			}
			else if (8 == mV_CC_DEVICE_INFO.nTLayerType)
			{
				CamlDeviceInfo camlDeviceInfo = new CamlDeviceInfo();
				camlDeviceInfo.TLayerType = (DeviceTLayerType)mV_CC_DEVICE_INFO.nTLayerType;
				camlDeviceInfo.DevTypeInfo = mV_CC_DEVICE_INFO.nDevTypeInfo;
				MvCCDll.MV_CamL_DEV_INFO mV_CamL_DEV_INFO = (MvCCDll.MV_CamL_DEV_INFO)MvCCDll.ByteToStruct(mV_CC_DEVICE_INFO.SpecialInfo.stCamLInfo, typeof(MvCCDll.MV_CamL_DEV_INFO));
				camlDeviceInfo.PortID = mV_CamL_DEV_INFO.chPortID;
				camlDeviceInfo.ModelName = mV_CamL_DEV_INFO.chModelName;
				camlDeviceInfo.DeviceVersion = mV_CamL_DEV_INFO.chDeviceVersion;
				camlDeviceInfo.FamilyName = mV_CamL_DEV_INFO.chFamilyName;
				camlDeviceInfo.ManufacturerName = mV_CamL_DEV_INFO.chManufacturerName;
				camlDeviceInfo.SerialNumber = mV_CamL_DEV_INFO.chSerialNumber;
				devInfoList.Add(camlDeviceInfo);
			}
			else if (256 == mV_CC_DEVICE_INFO.nTLayerType)
			{
				CXPDeviceInfo cXPDeviceInfo = new CXPDeviceInfo();
				cXPDeviceInfo.TLayerType = (DeviceTLayerType)mV_CC_DEVICE_INFO.nTLayerType;
				cXPDeviceInfo.DevTypeInfo = mV_CC_DEVICE_INFO.nDevTypeInfo;
				MvCCDll.MV_CXP_DEVICE_INFO mV_CXP_DEVICE_INFO = (MvCCDll.MV_CXP_DEVICE_INFO)MvCCDll.ByteToStruct(mV_CC_DEVICE_INFO.SpecialInfo.stCXPInfo, typeof(MvCCDll.MV_CXP_DEVICE_INFO));
				cXPDeviceInfo.ManufacturerName = mV_CXP_DEVICE_INFO.chManufacturerInfo;
				cXPDeviceInfo.ModelName = mV_CXP_DEVICE_INFO.chModelName;
				cXPDeviceInfo.DeviceVersion = mV_CXP_DEVICE_INFO.chDeviceVersion;
				cXPDeviceInfo.SerialNumber = mV_CXP_DEVICE_INFO.chSerialNumber;
				cXPDeviceInfo.UserDefinedName = ByteArrayToString(mV_CXP_DEVICE_INFO.chUserDefinedName);
				cXPDeviceInfo.DeviceID = mV_CXP_DEVICE_INFO.chDeviceID;
				cXPDeviceInfo.InterfaceID = mV_CXP_DEVICE_INFO.chInterfaceID;
				devInfoList.Add(cXPDeviceInfo);
			}
			else if (128 == mV_CC_DEVICE_INFO.nTLayerType)
			{
				CameraLinkDeviceInfo cameraLinkDeviceInfo = new CameraLinkDeviceInfo();
				cameraLinkDeviceInfo.TLayerType = (DeviceTLayerType)mV_CC_DEVICE_INFO.nTLayerType;
				cameraLinkDeviceInfo.DevTypeInfo = mV_CC_DEVICE_INFO.nDevTypeInfo;
				MvCCDll.MV_CML_DEVICE_INFO mV_CML_DEVICE_INFO = (MvCCDll.MV_CML_DEVICE_INFO)MvCCDll.ByteToStruct(mV_CC_DEVICE_INFO.SpecialInfo.stCMLInfo, typeof(MvCCDll.MV_CML_DEVICE_INFO));
				cameraLinkDeviceInfo.ManufacturerName = mV_CML_DEVICE_INFO.chManufacturerInfo;
				cameraLinkDeviceInfo.ModelName = mV_CML_DEVICE_INFO.chModelName;
				cameraLinkDeviceInfo.DeviceVersion = mV_CML_DEVICE_INFO.chDeviceVersion;
				cameraLinkDeviceInfo.SerialNumber = mV_CML_DEVICE_INFO.chSerialNumber;
				cameraLinkDeviceInfo.UserDefinedName = ByteArrayToString(mV_CML_DEVICE_INFO.chUserDefinedName);
				cameraLinkDeviceInfo.DeviceID = mV_CML_DEVICE_INFO.chDeviceID;
				cameraLinkDeviceInfo.InterfaceID = mV_CML_DEVICE_INFO.chInterfaceID;
				devInfoList.Add(cameraLinkDeviceInfo);
			}
			else if (512 == mV_CC_DEVICE_INFO.nTLayerType)
			{
				XoFDeviceInfo xoFDeviceInfo = new XoFDeviceInfo();
				xoFDeviceInfo.TLayerType = (DeviceTLayerType)mV_CC_DEVICE_INFO.nTLayerType;
				xoFDeviceInfo.DevTypeInfo = mV_CC_DEVICE_INFO.nDevTypeInfo;
				MvCCDll.MV_XOF_DEVICE_INFO mV_XOF_DEVICE_INFO = (MvCCDll.MV_XOF_DEVICE_INFO)MvCCDll.ByteToStruct(mV_CC_DEVICE_INFO.SpecialInfo.stXoFInfo, typeof(MvCCDll.MV_XOF_DEVICE_INFO));
				xoFDeviceInfo.ManufacturerName = mV_XOF_DEVICE_INFO.chManufacturerInfo;
				xoFDeviceInfo.ModelName = mV_XOF_DEVICE_INFO.chModelName;
				xoFDeviceInfo.DeviceVersion = mV_XOF_DEVICE_INFO.chDeviceVersion;
				xoFDeviceInfo.SerialNumber = mV_XOF_DEVICE_INFO.chSerialNumber;
				xoFDeviceInfo.UserDefinedName = ByteArrayToString(mV_XOF_DEVICE_INFO.chUserDefinedName);
				xoFDeviceInfo.DeviceID = mV_XOF_DEVICE_INFO.chDeviceID;
				xoFDeviceInfo.InterfaceID = mV_XOF_DEVICE_INFO.chInterfaceID;
				devInfoList.Add(xoFDeviceInfo);
			}
		}
	}
}
