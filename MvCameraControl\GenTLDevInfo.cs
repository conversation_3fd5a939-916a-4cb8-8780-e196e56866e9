namespace MvCameraControl;

internal class GenTLDevInfo : IGenTLDevInfo
{
	/// <summary>
	/// GenTL接口ID
	/// </summary>
	public string InterfaceID { get; set; }

	/// <summary>
	/// 设备ID
	/// </summary>
	public string DeviceID { get; set; }

	/// <summary>
	/// 供应商名字
	/// </summary>
	public string VendorName { get; set; }

	/// <summary>
	/// 型号名字
	/// </summary>
	public string ModelName { get; set; }

	/// <summary>
	/// 传输层类型
	/// </summary>
	public string TLType { get; set; }

	/// <summary>
	/// 设备显示名称
	/// </summary>
	public string DisplayName { get; set; }

	/// <summary>
	/// 用户自定义名字
	/// </summary>
	public string UserDefinedName { get; set; }

	/// <summary>
	/// 序列号
	/// </summary>
	public string SerialNumber { get; set; }

	/// <summary>
	/// 设备版本号
	/// </summary>
	public string DeviceVersion { get; set; }

	/// <summary>
	/// GenTL的cti文件索引
	/// </summary>
	public uint CtiIndex { get; set; }
}
