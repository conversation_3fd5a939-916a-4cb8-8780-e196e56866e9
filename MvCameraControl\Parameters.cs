using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace MvCameraControl;

/// <summary>
/// 参数实现类
/// </summary>
internal class Parameters : IParameters
{
	/// <summary>
	/// 设备句柄
	/// </summary>
	private IntPtr _deviceHandle;

	/// <summary>
	/// FileAccessRead到Byte数组时的默认文件大小
	/// </summary>
	private static uint defaultFileAccessReadBufferSize = 1048576u;

	/// <summary>
	/// 构造函数
	/// </summary>
	/// <param name="deviceHandle"></param>
	public Parameters(IntPtr deviceHandle)
	{
		_deviceHandle = deviceHandle;
	}

	public int InvalidateNodes()
	{
		return MvCCDll.MV_CC_InvalidateNodes(_deviceHandle);
	}

	public int GetIntValue(string key, out IIntValue value)
	{
		IntValue intValue = (IntValue)(value = new IntValue());
		MvCCDll.MVCC_INTVALUE_EX pIntValue = new MvCCDll.MVCC_INTVALUE_EX
		{
			nReserved = new uint[16]
		};
		int result = MvCCDll.MV_CC_GetIntValueEx(_deviceHandle, key, ref pIntValue);
		intValue.CurValue = pIntValue.nCurValue;
		intValue.Inc = pIntValue.nInc;
		intValue.Min = pIntValue.nMin;
		intValue.Max = pIntValue.nMax;
		return result;
	}

	public int SetIntValue(string key, long value)
	{
		return MvCCDll.MV_CC_SetIntValueEx(_deviceHandle, key, value);
	}

	public int GetEnumValue(string key, out IEnumValue value)
	{
		value = null;
		int num = 0;
		EnumValue enumValue = new EnumValue();
		try
		{
			MvCCDll.MVCC_ENUMVALUE_EX pEnumValue = new MvCCDll.MVCC_ENUMVALUE_EX
			{
				nSupportValue = new uint[256],
				nReserved = new uint[4]
			};
			num = MvCCDll.MV_CC_GetEnumValueEx(_deviceHandle, key, ref pEnumValue);
			MvCCDll.MVCC_ENUMENTRY pstEnumEntry = new MvCCDll.MVCC_ENUMENTRY
			{
				nValue = pEnumValue.nCurValue
			};
			num = MvCCDll.MV_CC_GetEnumEntrySymbolic(_deviceHandle, key, ref pstEnumEntry);
			if (num != 0)
			{
				return num;
			}
			EnumEntry enumEntry = new EnumEntry();
			enumEntry.Value = pEnumValue.nCurValue;
			enumEntry.Symbolic = InnerTools.ByteArrayToString(pstEnumEntry.chSymbolic);
			EnumEntry[] array = new EnumEntry[pEnumValue.nSupportedNum];
			for (int i = 0; i < pEnumValue.nSupportedNum; i++)
			{
				MvCCDll.MVCC_ENUMENTRY pstEnumEntry2 = new MvCCDll.MVCC_ENUMENTRY
				{
					nValue = pEnumValue.nSupportValue[i]
				};
				num = MvCCDll.MV_CC_GetEnumEntrySymbolic(_deviceHandle, key, ref pstEnumEntry2);
				array[i] = new EnumEntry();
				array[i].Value = pstEnumEntry2.nValue;
				array[i].Symbolic = InnerTools.ByteArrayToString(pstEnumEntry2.chSymbolic);
			}
			enumValue.CurEnumEntry = enumEntry;
			enumValue.SupportedNum = pEnumValue.nSupportedNum;
			IEnumEntry[] supportEnumEntries = array;
			enumValue.SupportEnumEntries = supportEnumEntries;
		}
		catch (EntryPointNotFoundException)
		{
			MvCCDll.MVCC_ENUMVALUE pEnumValue2 = new MvCCDll.MVCC_ENUMVALUE
			{
				nSupportValue = new uint[256],
				nReserved = new uint[4]
			};
			num = MvCCDll.MV_CC_GetEnumValue(_deviceHandle, key, ref pEnumValue2);
			MvCCDll.MVCC_ENUMENTRY pstEnumEntry3 = new MvCCDll.MVCC_ENUMENTRY
			{
				nValue = pEnumValue2.nCurValue
			};
			num = MvCCDll.MV_CC_GetEnumEntrySymbolic(_deviceHandle, key, ref pstEnumEntry3);
			if (num != 0)
			{
				return num;
			}
			EnumEntry enumEntry2 = new EnumEntry();
			enumEntry2.Value = pEnumValue2.nCurValue;
			enumEntry2.Symbolic = InnerTools.ByteArrayToString(pstEnumEntry3.chSymbolic);
			EnumEntry[] array2 = new EnumEntry[pEnumValue2.nSupportedNum];
			for (int j = 0; j < pEnumValue2.nSupportedNum; j++)
			{
				MvCCDll.MVCC_ENUMENTRY pstEnumEntry4 = new MvCCDll.MVCC_ENUMENTRY
				{
					nValue = pEnumValue2.nSupportValue[j]
				};
				num = MvCCDll.MV_CC_GetEnumEntrySymbolic(_deviceHandle, key, ref pstEnumEntry4);
				array2[j] = new EnumEntry();
				array2[j].Value = pstEnumEntry4.nValue;
				array2[j].Symbolic = InnerTools.ByteArrayToString(pstEnumEntry4.chSymbolic);
			}
			enumValue.CurEnumEntry = enumEntry2;
			enumValue.SupportedNum = pEnumValue2.nSupportedNum;
			IEnumEntry[] supportEnumEntries = array2;
			enumValue.SupportEnumEntries = supportEnumEntries;
		}
		value = enumValue;
		return num;
	}

	public int SetEnumValue(string key, uint value)
	{
		return MvCCDll.MV_CC_SetEnumValue(_deviceHandle, key, value);
	}

	public int GetEnumEntrySymbolic(string key, uint value, out string symbolic)
	{
		MvCCDll.MVCC_ENUMENTRY pstEnumEntry = new MvCCDll.MVCC_ENUMENTRY
		{
			nValue = value,
			chSymbolic = new byte[64],
			nReserved = new uint[4]
		};
		int result = MvCCDll.MV_CC_GetEnumEntrySymbolic(_deviceHandle, key, ref pstEnumEntry);
		symbolic = InnerTools.ByteArrayToString(pstEnumEntry.chSymbolic);
		return result;
	}

	public int SetEnumValueByString(string key, string value)
	{
		return MvCCDll.MV_CC_SetEnumValueByString(_deviceHandle, key, value);
	}

	public int GetFloatValue(string key, out IFloatValue value)
	{
		FloatValue floatValue = (FloatValue)(value = new FloatValue());
		MvCCDll.MVCC_FLOATVALUE pFloatValue = new MvCCDll.MVCC_FLOATVALUE
		{
			nReserved = new uint[4]
		};
		int result = MvCCDll.MV_CC_GetFloatValue(_deviceHandle, key, ref pFloatValue);
		floatValue.CurValue = pFloatValue.fCurValue;
		floatValue.Min = pFloatValue.fMin;
		floatValue.Max = pFloatValue.fMax;
		return result;
	}

	public int SetFloatValue(string key, float value)
	{
		return MvCCDll.MV_CC_SetFloatValue(_deviceHandle, key, value);
	}

	public int GetBoolValue(string key, out bool value)
	{
		value = false;
		return MvCCDll.MV_CC_GetBoolValue(_deviceHandle, key, ref value);
	}

	public int SetBoolValue(string key, bool value)
	{
		return MvCCDll.MV_CC_SetBoolValue(_deviceHandle, key, value);
	}

	public int GetStringValue(string key, out IStringValue value)
	{
		StringValue stringValue = (StringValue)(value = new StringValue());
		MvCCDll.MVCC_STRINGVALUE pStringValue = new MvCCDll.MVCC_STRINGVALUE
		{
			nReserved = new uint[2]
		};
		int result = MvCCDll.MV_CC_GetStringValue(_deviceHandle, key, ref pStringValue);
		stringValue.CurValue = pStringValue.chCurValue;
		stringValue.MaxLength = (ulong)pStringValue.nMaxLength;
		return result;
	}

	public int SetStringValue(string key, string value)
	{
		return MvCCDll.MV_CC_SetStringValue(_deviceHandle, key, value);
	}

	public int SetCommandValue(string key)
	{
		return MvCCDll.MV_CC_SetCommandValue(_deviceHandle, key);
	}

	public int FeatureLoad(string filePath)
	{
		return MvCCDll.MV_CC_FeatureLoad(_deviceHandle, filePath);
	}

	public int FeatureSave(string filePath)
	{
		return MvCCDll.MV_CC_FeatureSave(_deviceHandle, filePath);
	}

	public int ReadMemory(long address, long length, out byte[] buffer)
	{
		buffer = new byte[length];
		return MvCCDll.MV_CC_ReadMemory(_deviceHandle, Marshal.UnsafeAddrOfPinnedArrayElement((Array)buffer, 0), address, length);
	}

	public int WriteMemory(long address, long length, byte[] buffer)
	{
		if (buffer == null)
		{
			return -2147483644;
		}
		return MvCCDll.MV_CC_WriteMemory(_deviceHandle, Marshal.UnsafeAddrOfPinnedArrayElement((Array)buffer, 0), address, length);
	}

	public int GetGenICamXML(out string xmlData)
	{
		byte[] array = null;
		uint pnDataLen = 0u;
		xmlData = string.Empty;
		int num = 0;
		num = MvCCDll.MV_XML_GetGenICamXML(_deviceHandle, IntPtr.Zero, 0u, ref pnDataLen);
		if (pnDataLen != 0)
		{
			array = new byte[pnDataLen];
			num = MvCCDll.MV_XML_GetGenICamXML(_deviceHandle, Marshal.UnsafeAddrOfPinnedArrayElement((Array)array, 0), pnDataLen, ref pnDataLen);
		}
		if (num == 0)
		{
			xmlData = InnerTools.ByteArrayToString(array);
		}
		return num;
	}

	public int GetNodeAccessMode(string name, out XmlAccessMode mode)
	{
		mode = XmlAccessMode.Undefined;
		MvCCDll.MV_XML_AccessMode pAccessMode = MvCCDll.MV_XML_AccessMode.AM_Undefined;
		int num = MvCCDll.MV_XML_GetNodeAccessMode(_deviceHandle, name, ref pAccessMode);
		if (num == 0)
		{
			mode = (XmlAccessMode)pAccessMode;
		}
		return num;
	}

	public int GetNodeInterfaceType(string name, out XmlInterfaceType type)
	{
		type = XmlInterfaceType.IValue;
		MvCCDll.MV_XML_InterfaceType pInterfaceType = MvCCDll.MV_XML_InterfaceType.IFT_IValue;
		int num = MvCCDll.MV_XML_GetNodeInterfaceType(_deviceHandle, name, ref pInterfaceType);
		if (num == 0)
		{
			type = (XmlInterfaceType)pInterfaceType;
		}
		return num;
	}

	public int FileAccessRead(string deviceFileName, string localFilePath)
	{
		MvCCDll.MV_CC_FILE_ACCESS pstFileAccess = new MvCCDll.MV_CC_FILE_ACCESS
		{
			nReserved = new uint[32],
			pUserFileName = localFilePath,
			pDevFileName = deviceFileName
		};
		return MvCCDll.MV_CC_FileAccessRead(_deviceHandle, ref pstFileAccess);
	}

	public int FileAccessRead(string devFileName, out byte[] buffer)
	{
		int num = 0;
		buffer = new byte[defaultFileAccessReadBufferSize];
		MvCCDll.MV_CC_FILE_ACCESS_EX pstFileAccessEx = new MvCCDll.MV_CC_FILE_ACCESS_EX
		{
			nReserved = new uint[32],
			pDevFileName = devFileName,
			pUserFileBuf = Marshal.UnsafeAddrOfPinnedArrayElement((Array)buffer, 0),
			nFileBufSize = defaultFileAccessReadBufferSize
		};
		num = MvCCDll.MV_CC_FileAccessReadEx(_deviceHandle, ref pstFileAccessEx);
		if (num == -2147483638)
		{
			buffer = new byte[pstFileAccessEx.nFileBufLen];
			pstFileAccessEx.pUserFileBuf = Marshal.UnsafeAddrOfPinnedArrayElement((Array)buffer, 0);
			pstFileAccessEx.nFileBufSize = defaultFileAccessReadBufferSize;
			num = MvCCDll.MV_CC_FileAccessReadEx(_deviceHandle, ref pstFileAccessEx);
		}
		Array.Resize(ref buffer, (int)pstFileAccessEx.nFileBufLen);
		return num;
	}

	public int FileAccessWrite(string deviceFileName, string localFilePath)
	{
		MvCCDll.MV_CC_FILE_ACCESS pstFileAccess = new MvCCDll.MV_CC_FILE_ACCESS
		{
			nReserved = new uint[32],
			pUserFileName = localFilePath,
			pDevFileName = deviceFileName
		};
		return MvCCDll.MV_CC_FileAccessWrite(_deviceHandle, ref pstFileAccess);
	}

	public int FileAccessWrite(string devFileName, byte[] buffer)
	{
		if (buffer == null)
		{
			return -2147483644;
		}
		MvCCDll.MV_CC_FILE_ACCESS_EX pstFileAccessEx = new MvCCDll.MV_CC_FILE_ACCESS_EX
		{
			pUserFileBuf = Marshal.UnsafeAddrOfPinnedArrayElement((Array)buffer, 0),
			nFileBufLen = (uint)buffer.Length,
			nFileBufSize = (uint)buffer.Length,
			pDevFileName = devFileName,
			nReserved = new uint[32]
		};
		return MvCCDll.MV_CC_FileAccessWriteEx(_deviceHandle, ref pstFileAccessEx);
	}

	public int GetFileAccessProgress(out long completed, out long total)
	{
		MvCCDll.MV_CC_FILE_ACCESS_PROGRESS pstFileAccessProgress = new MvCCDll.MV_CC_FILE_ACCESS_PROGRESS
		{
			nReserved = new uint[8]
		};
		int result = MvCCDll.MV_CC_GetFileAccessProgress(_deviceHandle, ref pstFileAccessProgress);
		completed = pstFileAccessProgress.nCompleted;
		total = pstFileAccessProgress.nTotal;
		return result;
	}

	public int FeatureLoadEx(string filePath, out List<INodeError> nodeErrors)
	{
		nodeErrors = new List<INodeError>();
		int num = 0;
		MvCCDll.MVCC_NODE_ERROR_LIST pNodeErrorList = default(MvCCDll.MVCC_NODE_ERROR_LIST);
		num = MvCCDll.MV_CC_FeatureLoadEx(_deviceHandle, filePath, ref pNodeErrorList);
		for (int i = 0; i < pNodeErrorList.nErrorNum; i++)
		{
			NodeError nodeError = new NodeError();
			nodeError.NodeName = pNodeErrorList.stNodeError[i].strName;
			nodeError.ErrorType = (NodeErrorType)pNodeErrorList.stNodeError[i].enErrCode;
			nodeErrors.Add(nodeError);
		}
		return num;
	}

	public int FeatureSaveEx(string filePath, List<string> nodeNames)
	{
		if (nodeNames == null)
		{
			return -2147483644;
		}
		MvCCDll.MVCC_NODE_NAME_LIST pNodeNameList = new MvCCDll.MVCC_NODE_NAME_LIST
		{
			stNodeName = new MvCCDll.MVCC_NODE_NAME[256],
			nReserved = new uint[4]
		};
		for (int i = 0; i < nodeNames.Count; i++)
		{
			pNodeNameList.stNodeName[i].strName = nodeNames[i];
			pNodeNameList.nNodeNum++;
		}
		return MvCCDll.MV_CC_FeatureSaveEx(_deviceHandle, filePath, ref pNodeNameList);
	}
}
