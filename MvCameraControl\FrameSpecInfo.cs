namespace MvCameraControl;

/// <summary>
/// 图像帧的水印信息
/// </summary>
public class FrameSpecInfo
{
	/// <summary>
	/// 设备水印时标
	/// </summary>
	public uint SecondCount { get; set; }

	/// <summary>
	/// 周期数
	/// </summary>
	public uint CycleCount { get; set; }

	/// <summary>
	/// 周期偏移量
	/// </summary>
	public uint CycleOffset { get; set; }

	/// <summary>
	/// 增益
	/// </summary>
	public float Gain { get; set; }

	/// <summary>
	/// 曝光时间
	/// </summary>
	public float ExposureTime { get; set; }

	/// <summary>
	/// 平均亮度
	/// </summary>
	public uint AverageBrightness { get; set; }

	/// <summary>
	/// 白平衡红色通道
	/// </summary>
	public uint Red { get; set; }

	/// <summary>
	/// 白平衡绿色通道
	/// </summary>
	public uint Green { get; set; }

	/// <summary>
	/// 白平衡蓝色通道
	/// </summary>
	public uint Blue { get; set; }

	/// <summary>
	/// 总帧数
	/// </summary>
	public uint FrameCount { get; set; }

	/// <summary>
	/// 触发计数
	/// </summary>
	public uint TriggerIndex { get; set; }

	/// <summary>
	/// 输入
	/// </summary>
	public uint Input { get; set; }

	/// <summary>
	/// 输出
	/// </summary>
	public uint Output { get; set; }

	/// <summary>
	/// ROI区域，水平偏移量
	/// </summary>
	public uint OffsetX { get; set; }

	/// <summary>
	/// ROI区域，垂直偏移量
	/// </summary>
	public uint OffsetY { get; set; }
}
