using System;

namespace MvCameraControl;

internal class CamlDevice : Device, ICamlDevice, IDevice, IDisposable
{
	public CamlDevice(IDeviceInfo deviceInfo)
		: base(deviceInfo)
	{
	}

	/// <summary>
	/// 设置设备波特率 
	/// </summary>
	/// <param name="baudrate">波特率</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int SetDeviceBaudrate(CameraLinkBaudrate baudrate)
	{
		return MvCCDll.MV_CAML_SetDeviceBaudrate(base.DevHandle, (uint)baudrate);
	}

	/// <summary>
	/// 获取设备波特率 
	/// </summary>
	/// <param name="baudrate">波特率</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int GetDeviceBaudrate(out CameraLinkBaudrate baudrate)
	{
		baudrate = CameraLinkBaudrate.B9600;
		uint pnCurrentBaudrate = 0u;
		int num = MvCCDll.MV_CAML_GetDeviceBaudrate(base.DevHandle, ref pnCurrentBaudrate);
		if (num == 0)
		{
			baudrate = (CameraLinkBaudrate)pnCurrentBaudrate;
		}
		return num;
	}

	/// <summary>
	/// 获取设备与主机间连接支持的波特率 
	/// </summary>
	/// <param name="baudrateAbility">所支持波特率的或运算结果，单个波特率参考 <see cref="T:MvCameraControl.CameraLinkBaudrate" /></param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int GetSupportBaudrates(out uint baudrateAbility)
	{
		baudrateAbility = 0u;
		return MvCCDll.MV_CAML_GetSupportBaudrates(base.DevHandle, ref baudrateAbility);
	}

	/// <summary>
	/// 设置串口操作等待时长
	/// </summary>
	/// <param name="timeoutInMS">串口操作的等待时长，单位为ms</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int SetGenCPTimeOut(uint timeoutInMS)
	{
		return MvCCDll.MV_CAML_SetGenCPTimeOut(base.DevHandle, timeoutInMS);
	}
}
