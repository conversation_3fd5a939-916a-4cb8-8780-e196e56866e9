namespace MvCameraControl;

/// <summary>
/// CoaXPress设备信息
/// </summary>
internal class CXPDeviceInfo : ICXPDeviceInfo, IDeviceInfo
{
	/// <summary>
	/// 设备接口类型
	/// </summary>
	public DeviceTLayerType TLayerType { get; set; }

	/// <summary>
	/// 制造商信息
	/// </summary>
	public string ManufacturerName { get; set; }

	/// <summary>
	/// 设备型号
	/// </summary>
	public string ModelName { get; set; }

	/// <summary>
	/// 设备版本
	/// </summary>
	public string DeviceVersion { get; set; }

	/// <summary>
	/// 设备序列号
	/// </summary>
	public string SerialNumber { get; set; }

	/// <summary>
	/// 用户自定义名称
	/// </summary>
	public string UserDefinedName { get; set; }

	/// <summary>
	/// 设备类型信息，7 - 0 bit: 预留，15 - 8 bit：产品子类别，23 - 16 bit：产品类型，31 - 24bit：产品线（如: 0x01 标准产品；0x02 3D产品；0x03 智能ID产品）
	/// </summary>
	public uint DevTypeInfo { get; set; }

	/// <summary>
	/// 相机ID
	/// </summary>
	public string DeviceID { get; set; }

	/// <summary>
	/// 采集卡ID
	/// </summary>
	public string InterfaceID { get; set; }
}
