using System;

namespace MvCameraControl;

internal class ImageRender : IImageRender
{
	private IntPtr _devHandle = IntPtr.Zero;

	public ImageRender(IntPtr devHandle)
	{
		_devHandle = devHandle;
	}

	public int DisplayOneFrame(IntPtr hWnd, IImage image, RenderMode mode = RenderMode.Default)
	{
		if (image == null)
		{
			return -**********;
		}
		MvCCDll.MV_CC_IMAGE pDisplayInfoEx = new MvCCDll.MV_CC_IMAGE
		{
			nWidth = image.Width,
			nHeight = image.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)image.PixelType,
			pImageBuf = image.PixelDataPtr,
			nImageLen = image.ImageSize,
			nImageBufSize = image.ImageSize
		};
		return MvCCDll.MV_CC_DisplayOneFrameEx2(_devHandle, hWnd, ref pDisplayInfoEx, (uint)mode);
	}

	public int DisplayOneFrame(IntPtr hWnd, IntPtr imageDataPtr, uint imageLen, uint width, uint height, MvGvspPixelType PixelType, RenderMode mode = RenderMode.Default)
	{
		MvCCDll.MV_DISPLAY_FRAME_INFO_EX pDisplayInfoEx = new MvCCDll.MV_DISPLAY_FRAME_INFO_EX
		{
			nWidth = width,
			nHeight = height,
			enPixelType = (MvCCDll.MvGvspPixelType)PixelType,
			pData = imageDataPtr,
			nDataLen = imageLen,
			enRenderMode = (uint)mode
		};
		return MvCCDll.MV_CC_DisplayOneFrameEx(_devHandle, hWnd, ref pDisplayInfoEx);
	}

	public int DisplayOneFrame(IntPtr hWnd, IntPtr imageDataPtr, ImageInfo imageInfo, RenderMode mode = RenderMode.Default)
	{
		if (imageInfo == null)
		{
			return -**********;
		}
		MvCCDll.MV_CC_IMAGE pDisplayInfoEx = new MvCCDll.MV_CC_IMAGE
		{
			nWidth = imageInfo.Width,
			nHeight = imageInfo.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)imageInfo.PixelType,
			pImageBuf = imageDataPtr,
			nImageLen = imageInfo.ImageSize,
			nImageBufSize = imageInfo.ImageSize
		};
		return MvCCDll.MV_CC_DisplayOneFrameEx2(_devHandle, hWnd, ref pDisplayInfoEx, (uint)mode);
	}

	public int DrawCircle(MvCircle circle, MvColor color, uint lineWidth)
	{
		MvCCDll.MVCC_CIRCLE_INFO pCircleInfo = default(MvCCDll.MVCC_CIRCLE_INFO);
		pCircleInfo.stCenterPoint = default(MvCCDll.MVCC_POINTF);
		pCircleInfo.stCenterPoint.fX = circle.CenterPoint.X;
		pCircleInfo.stCenterPoint.fY = circle.CenterPoint.Y;
		pCircleInfo.fR1 = circle.R1;
		pCircleInfo.fR2 = circle.R2;
		pCircleInfo.stColor = default(MvCCDll.MVCC_COLORF);
		pCircleInfo.stColor.fR = color.R;
		pCircleInfo.stColor.fG = color.G;
		pCircleInfo.stColor.fB = color.B;
		pCircleInfo.nLineWidth = lineWidth;
		return MvCCDll.MV_CC_DrawCircle(_devHandle, ref pCircleInfo);
	}

	public int DrawLine(MvLine line, MvColor color, uint lineWidth)
	{
		MvCCDll.MVCC_LINES_INFO pLinesInfo = default(MvCCDll.MVCC_LINES_INFO);
		pLinesInfo.stStartPoint = default(MvCCDll.MVCC_POINTF);
		pLinesInfo.stStartPoint.fX = line.StartPoint.X;
		pLinesInfo.stStartPoint.fY = line.StartPoint.Y;
		pLinesInfo.stEndPoint = default(MvCCDll.MVCC_POINTF);
		pLinesInfo.stEndPoint.fX = line.EndPoint.X;
		pLinesInfo.stEndPoint.fY = line.EndPoint.Y;
		pLinesInfo.stColor = default(MvCCDll.MVCC_COLORF);
		pLinesInfo.stColor.fR = color.R;
		pLinesInfo.stColor.fG = color.G;
		pLinesInfo.stColor.fB = color.B;
		pLinesInfo.nLineWidth = lineWidth;
		return MvCCDll.MV_CC_DrawLines(_devHandle, ref pLinesInfo);
	}

	public int DrawRect(MvRect rect, MvColor color, uint lineWidth)
	{
		MvCCDll.MVCC_RECT_INFO pRectInfo = default(MvCCDll.MVCC_RECT_INFO);
		pRectInfo.fTop = rect.Top;
		pRectInfo.fBottom = rect.Bottom;
		pRectInfo.fLeft = rect.Left;
		pRectInfo.fRight = rect.Right;
		pRectInfo.stColor = default(MvCCDll.MVCC_COLORF);
		pRectInfo.stColor.fR = color.R;
		pRectInfo.stColor.fG = color.G;
		pRectInfo.stColor.fB = color.B;
		pRectInfo.nLineWidth = lineWidth;
		return MvCCDll.MV_CC_DrawRect(_devHandle, ref pRectInfo);
	}
}
