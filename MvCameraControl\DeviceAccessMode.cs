namespace MvCameraControl;

/// <summary>
/// 设备访问权限，只支持GigE设备
/// </summary>
public enum DeviceAccessMode
{
	/// <summary>
	/// 独占权限，其他APP只允许读CCP寄存器 
	/// </summary>
	AccessExclusive = 1,
	/// <summary>
	/// 可以从5模式下抢占权限，然后以独占权限打开
	/// </summary>
	AccessExclusiveWithSwitch,
	/// <summary>
	/// 控制权限，其他APP允许读所有寄存器 
	/// </summary>
	AccessControl,
	/// <summary>
	/// 可以从5模式下抢占权限，然后以控制权限打开 
	/// </summary>
	AccessControlWithSwitch,
	/// <summary>
	/// 以可被抢占的控制权限打开 
	/// </summary>
	AccessControlSwitchEnable,
	/// <summary>
	/// 可以从5模式下抢占权限，然后以可被抢占的控制权限打开 
	/// </summary>
	AccessControlSwitchEnableWithKey,
	/// <summary>
	/// 读模式打开设备，适用于控制权限下 
	/// </summary>
	AccessMonitor
}
