using System;
using System.Runtime.InteropServices;

namespace MvCameraControl;

/// <summary>
/// 基于内存池的Image对象，用于图像处理相关接口
/// </summary>
internal class MvPooledImage : MvImage
{
	private ByteBlock _byteBlock;

	private ByteBlockPool _byteBlockPool;

	public override IntPtr PixelDataPtr => Marshal.UnsafeAddrOfPinnedArrayElement((Array)_byteBlock.ByteArray, 0);

	public override byte[] PixelData => _byteBlock.ByteArray;

	/// <summary>
	/// 根据图像宽、高和像素格式创建对象，内部申请内存
	/// </summary>
	/// <param name="width">图像宽度</param>
	/// <param name="height">图像高度</param>
	/// <param name="pixelType">像素格式</param>
	/// <param name="handle">相机句柄，用于ToBitmap时像素格式转换</param>
	/// <param name="pool">内存池</param>
	public MvPooledImage(uint width, uint height, MvGvspPixelType pixelType, IntPtr handle, ByteBlockPool pool)
	{
		_width = width;
		_height = height;
		_pixelType = pixelType;
		_deviceHandle = handle;
		_byteBlockPool = pool;
		ulong imageSize = InnerTools.GetImageSize(width, height, pixelType);
		_byteBlock = _byteBlockPool.GetByteBlock((long)imageSize);
		_pixelData = _byteBlock.ByteArray;
		_imageSize = imageSize;
	}

	/// <summary>
	/// 根据图像宽、高、像素格式、图像大小创建对象，内部申请内存
	/// </summary>
	/// <param name="width">图像宽度</param>
	/// <param name="height">图像高度</param>
	/// <param name="pixelType">像素格式</param>
	/// <param name="handle">相机句柄，用于ToBitmap时像素格式转换</param>
	/// <param name="imageSize">图像大小</param>
	/// <param name="pool">内存池</param>
	public MvPooledImage(uint width, uint height, MvGvspPixelType pixelType, ulong imageSize, IntPtr handle, ByteBlockPool pool)
	{
		_width = width;
		_height = height;
		_pixelType = pixelType;
		_deviceHandle = handle;
		_byteBlockPool = pool;
		_byteBlock = _byteBlockPool.GetByteBlock((long)imageSize);
		_pixelData = _byteBlock.ByteArray;
		_imageSize = imageSize;
	}

	/// <summary>
	/// 根据图像宽、高、像素格式、图像大小创建对象，外部传入内存池内存
	/// </summary>
	/// <param name="width"></param>
	/// <param name="height"></param>
	/// <param name="pixelType"></param>
	/// <param name="imageSize"></param>
	/// <param name="byteBlock"></param>
	/// <param name="handle"></param>
	/// <param name="pool"></param>
	public MvPooledImage(uint width, uint height, MvGvspPixelType pixelType, ulong imageSize, ByteBlock byteBlock, IntPtr handle, ByteBlockPool pool)
	{
		_width = width;
		_height = height;
		_pixelType = pixelType;
		_deviceHandle = handle;
		_byteBlockPool = pool;
		_byteBlock = byteBlock;
		_pixelData = _byteBlock.ByteArray;
		_imageSize = imageSize;
	}

	~MvPooledImage()
	{
		Dispose(disposing: false);
	}

	protected override void Dispose(bool disposing)
	{
		if (_isDisposed)
		{
			return;
		}
		if (_byteBlock != null)
		{
			if (disposing)
			{
				if (_byteBlockPool != null)
				{
					_byteBlockPool.FreeByteBlock(_byteBlock, disposing);
					_byteBlock = null;
				}
			}
			else
			{
				_byteBlock = null;
			}
		}
		_isDisposed = true;
	}
}
