using System;
using System.Collections.Generic;
using System.Net;
using System.Runtime.InteropServices;

namespace MvCameraControl;

/// <summary>
/// 设备枚举类，支持枚举GigE Vision、USB3 Vision相机，及采集卡上的相机（GigE Vision、CameraLink、CoaXPress、XoFlink） 
/// </summary>
public static class DeviceEnumerator
{
	/// <summary>
	/// 枚举设备
	/// </summary>
	/// <param name="TLayerType">设备接口类型</param>
	/// <param name="devInfoList">设备列表</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 设备接口类型为MV_GIGE_DEVICE时，枚举所有GigE设备，包含虚拟GigE设备和采集卡上的GigE设备。
	/// 设备接口类型为MV_USB_DEVICE时，枚举所有USB设备，包含虚拟USB设备。
	///
	/// 设备列表的内存是在SDK内部分配的，调用该接口时会进行设备列表内存的释放和申请，避免多线程枚举操作。
	///
	/// 枚举到设备后，通过<see cref="T:MvCameraControl.DeviceFactory" />创建设备实例。
	/// </remarks>
	public static int EnumDevices(DeviceTLayerType TLayerType, out List<IDeviceInfo> devInfoList)
	{
		return EnumDevicesImpl(TLayerType, out devInfoList);
	}

	/// <summary>
	/// 枚举设备，支持枚举指定厂商的设备
	/// </summary>
	/// <param name="TLayerType">设备接口类型</param>
	/// <param name="manufacturerName">厂商名称</param>
	/// <param name="devInfoList">设备列表</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 设备接口类型为MV_GIGE_DEVICE时，枚举所有GigE设备，包含虚拟GigE设备和采集卡上的GigE设备。
	/// 设备接口类型为MV_USB_DEVICE时，枚举所有USB设备，包含虚拟USB设备。
	///
	/// 设备列表的内存是在SDK内部分配的，调用该接口时会进行设备列表内存的释放和申请，避免多线程枚举操作。
	///
	/// 枚举到设备后，通过<see cref="T:MvCameraControl.DeviceFactory" />创建设备实例。
	/// </remarks>
	public static int EnumDevicesEx(DeviceTLayerType TLayerType, string manufacturerName, out List<IDeviceInfo> devInfoList)
	{
		return EnumDevicesExImpl(TLayerType, manufacturerName, out devInfoList);
	}

	/// <summary>
	/// 枚举设备, 可指定排序方式枚举、根据厂商名字过滤
	/// </summary>
	/// <param name="TLayerType">设备接口类型</param>
	/// <param name="sortMethod">排序方式</param>
	/// <param name="manufacturerName">厂商名称</param>
	/// <param name="devInfoList">设备列表</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 设备接口类型为MV_GIGE_DEVICE时，仅枚举网络上的网口相机，不包含虚拟GigE相机和采集卡上的相机
	/// 设备接口类型为MV_USB_DEVICE时，枚举普通USB设备，不包含虚拟USB设备。
	///
	/// 设备列表的内存是在SDK内部分配的，调用该接口时会进行设备列表内存的释放和申请，避免多线程枚举操作。
	///
	/// 枚举到设备后，通过<see cref="T:MvCameraControl.DeviceFactory" />创建设备实例。
	/// </remarks>
	public static int EnumDevicesEx2(DeviceTLayerType TLayerType, SortMethod sortMethod, string manufacturerName, out List<IDeviceInfo> devInfoList)
	{
		return EnumDevicesEx2Impl(TLayerType, sortMethod, manufacturerName, out devInfoList);
	}

	/// <summary>
	/// 判断设备是否可达
	/// </summary>
	/// <param name="deviceInfo">设备信息</param>
	/// <param name="accessMode">访问权限</param>
	/// <returns>可达，返回true；不可达，返回false </returns>
	public static bool IsDeviceAccessible(IDeviceInfo deviceInfo, DeviceAccessMode accessMode)
	{
		return IsDeviceAccessibleImpl(deviceInfo, accessMode);
	}

	/// <summary>
	/// 设置GigE设备枚举超时时间，范围 1-UINT_MAX（包括1，不包括UINT_MAX） 
	/// </summary>
	/// <param name="timeoutInMS">超时时间</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public static int SetGigEDeviceEnumTimeout(uint timeoutInMS)
	{
		return SetGigEDeviceEnumTimeoutImpl(timeoutInMS);
	}

	/// <summary>
	/// 设置枚举命令的回复包类型 
	/// </summary>
	/// <param name="mode">回复包类型（默认广播），0-单播，1-广播 </param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public static int SetGigEDeviceDiscoryMode(uint mode)
	{
		return SetGigEDeviceDiscoryModeImpl(mode);
	}

	/// <summary>
	/// 获取主机串口列表 
	/// </summary>
	/// <param name="serialPortList">串口列表</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public static int GetSerialPortList(out List<string> serialPortList)
	{
		return GetSerialPortListImpl(out serialPortList);
	}

	/// <summary>
	/// 设置在指定的串口上枚举设备
	/// </summary>
	/// <param name="serialPortList">串口列表</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public static int SetEnumSerialPorts(List<string> serialPortList)
	{
		return SetEnumSerialPortsImpl(serialPortList);
	}

	/// <summary>
	/// 发出动作命令 
	/// </summary>
	/// <param name="actionCmdInfo">动作命令信息 </param>
	/// <param name="actionCmdResults">动作命令返回信息列表</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 仅GigEVision相机支持
	/// </remarks>
	public static int GigEIssueActionCommand(ActionCmdInfo actionCmdInfo, out List<ActionCmdResult> actionCmdResults)
	{
		return IssueActionCommandImpl(actionCmdInfo, out actionCmdResults);
	}

	/// <summary>
	/// 获取GigE设备组播状态 
	/// </summary>
	/// <param name="deviceInfo">设备信息</param>
	/// <param name="status">组播状态（true：组播状态；false：非组播）</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 仅GigEVision相机支持
	/// </remarks>
	public static int GetGigEMulticastStatus(IDeviceInfo deviceInfo, out bool status)
	{
		return GetMulticastStatusImpl(deviceInfo, out status);
	}

	private static int EnumDevicesImpl(DeviceTLayerType TLayerType, out List<IDeviceInfo> devInfoList)
	{
		devInfoList = new List<IDeviceInfo>();
		int num = 0;
		try
		{
			MvCCDll.MV_CC_DEVICE_INFO_LIST stDevList = default(MvCCDll.MV_CC_DEVICE_INFO_LIST);
			num = MvCCDll.MV_CC_EnumDevices((uint)TLayerType, ref stDevList);
			if (num == 0)
			{
				InnerTools.DevInfoListStruct2DevInfoList(stDevList, ref devInfoList);
			}
		}
		catch (Exception)
		{
			num = -2147483642;
		}
		return num;
	}

	private static int EnumDevicesExImpl(DeviceTLayerType TLayerType, string manufacturerName, out List<IDeviceInfo> devInfoList)
	{
		devInfoList = new List<IDeviceInfo>();
		MvCCDll.MV_CC_DEVICE_INFO_LIST stDevList = default(MvCCDll.MV_CC_DEVICE_INFO_LIST);
		int num = MvCCDll.MV_CC_EnumDevicesEx((uint)TLayerType, ref stDevList, manufacturerName);
		if (num == 0)
		{
			InnerTools.DevInfoListStruct2DevInfoList(stDevList, ref devInfoList);
		}
		return num;
	}

	private static int EnumDevicesEx2Impl(DeviceTLayerType TLayerType, SortMethod sortMethod, string manufacturerName, out List<IDeviceInfo> devInfoList)
	{
		devInfoList = new List<IDeviceInfo>();
		MvCCDll.MV_CC_DEVICE_INFO_LIST stDevList = default(MvCCDll.MV_CC_DEVICE_INFO_LIST);
		int num = MvCCDll.MV_CC_EnumDevicesEx2((uint)TLayerType, ref stDevList, manufacturerName, (MvCCDll.MV_SORT_METHOD)sortMethod);
		if (num == 0)
		{
			InnerTools.DevInfoListStruct2DevInfoList(stDevList, ref devInfoList);
		}
		return num;
	}

	/// <summary>
	/// 将用户的设备信息格式转换为SDK的内部设备信息格式
	/// </summary>
	/// <param name="mvDeviceInfo"></param>
	/// <param name="deviceInfo"></param>
	/// <returns></returns>
	private static int DeviceInfoToMv(ref MvCCDll.MV_CC_DEVICE_INFO mvDeviceInfo, ref IDeviceInfo deviceInfo)
	{
		if (DeviceTLayerType.MvGigEDevice == deviceInfo.TLayerType || DeviceTLayerType.MvVirGigEDevice == deviceInfo.TLayerType || DeviceTLayerType.MvGenTLGigEDevice == deviceInfo.TLayerType)
		{
			GigEDeviceInfo gigEDeviceInfo = (GigEDeviceInfo)deviceInfo;
			mvDeviceInfo.nMajorVer = gigEDeviceInfo.MajorVer;
			mvDeviceInfo.nMinorVer = gigEDeviceInfo.MinorVer;
			mvDeviceInfo.nMacAddrHigh = gigEDeviceInfo.MacAddrHigh;
			mvDeviceInfo.nMacAddrLow = gigEDeviceInfo.MacAddrLow;
			mvDeviceInfo.nTLayerType = (uint)gigEDeviceInfo.TLayerType;
			mvDeviceInfo.nDevTypeInfo = gigEDeviceInfo.DevTypeInfo;
			MvCCDll.MV_GIGE_DEVICE_INFO obj = new MvCCDll.MV_GIGE_DEVICE_INFO
			{
				nIpCfgOption = gigEDeviceInfo.IpCfgOption,
				nIpCfgCurrent = gigEDeviceInfo.IpCfgCurrent,
				nCurrentIp = gigEDeviceInfo.CurrentIp,
				nCurrentSubNetMask = gigEDeviceInfo.CurrentSubNetMask,
				nDefultGateWay = gigEDeviceInfo.DefultGateWay,
				chManufacturerName = gigEDeviceInfo.ManufacturerName,
				chModelName = gigEDeviceInfo.ModelName,
				chDeviceVersion = gigEDeviceInfo.DeviceVersion,
				chSerialNumber = gigEDeviceInfo.SerialNumber,
				chUserDefinedName = gigEDeviceInfo.UserDefinedName,
				nNetExport = gigEDeviceInfo.NetExport
			};
			if (obj.nReserved == null)
			{
				obj.nReserved = new uint[4];
			}
			obj.nReserved[0] = gigEDeviceInfo.HostIp;
			obj.nReserved[2] = gigEDeviceInfo.nMulticastIp;
			obj.nReserved[3] = gigEDeviceInfo.nMulticastPort;
			if (gigEDeviceInfo.VirtualDevice || DeviceTLayerType.MvVirGigEDevice == deviceInfo.TLayerType)
			{
				obj.nReserved[1] = 1u;
			}
			if (gigEDeviceInfo.GenTLDevice || DeviceTLayerType.MvGenTLGigEDevice == deviceInfo.TLayerType)
			{
				obj.nReserved[1] = 2u;
			}
			MvCCDll.StructToBytes(obj, mvDeviceInfo.SpecialInfo.stGigEInfo);
		}
		else if (DeviceTLayerType.MvUsbDevice == deviceInfo.TLayerType || DeviceTLayerType.MvVirUsbDevice == deviceInfo.TLayerType)
		{
			USBDeviceInfo uSBDeviceInfo = (USBDeviceInfo)deviceInfo;
			mvDeviceInfo.nTLayerType = (uint)uSBDeviceInfo.TLayerType;
			mvDeviceInfo.nDevTypeInfo = uSBDeviceInfo.DevTypeInfo;
			MvCCDll.MV_USB3_DEVICE_INFO obj2 = new MvCCDll.MV_USB3_DEVICE_INFO
			{
				chManufacturerName = uSBDeviceInfo.ManufacturerName,
				chModelName = uSBDeviceInfo.ModelName,
				chDeviceVersion = uSBDeviceInfo.DeviceVersion,
				chSerialNumber = uSBDeviceInfo.SerialNumber,
				chUserDefinedName = uSBDeviceInfo.UserDefinedName,
				CrtlInEndPoint = uSBDeviceInfo.CrtlInEndPoint,
				CrtlOutEndPoint = uSBDeviceInfo.CrtlOutEndPoint,
				StreamEndPoint = uSBDeviceInfo.StreamEndPoint,
				EventEndPoint = uSBDeviceInfo.EventEndPoint,
				idVendor = uSBDeviceInfo.VendorID,
				idProduct = uSBDeviceInfo.ProductID,
				nDeviceNumber = uSBDeviceInfo.DeviceNumber,
				chDeviceGUID = uSBDeviceInfo.DeviceGUID,
				chFamilyName = uSBDeviceInfo.FamilyName,
				nbcdUSB = uSBDeviceInfo.nbcdUSB
			};
			if (obj2.nReserved == null)
			{
				obj2.nReserved = new uint[3];
			}
			obj2.nReserved[0] = uSBDeviceInfo.DeviceAddress;
			if (uSBDeviceInfo.VirtualDevice || DeviceTLayerType.MvVirUsbDevice == deviceInfo.TLayerType)
			{
				obj2.nReserved[1] = 1u;
			}
			MvCCDll.StructToBytes(obj2, mvDeviceInfo.SpecialInfo.stUsb3VInfo);
		}
		else if (DeviceTLayerType.MvCameraLinkDevice == deviceInfo.TLayerType)
		{
			CamlDeviceInfo camlDeviceInfo = (CamlDeviceInfo)deviceInfo;
			mvDeviceInfo.nTLayerType = (uint)camlDeviceInfo.TLayerType;
			mvDeviceInfo.nDevTypeInfo = camlDeviceInfo.DevTypeInfo;
			MvCCDll.StructToBytes(new MvCCDll.MV_CamL_DEV_INFO
			{
				chPortID = camlDeviceInfo.PortID,
				chModelName = camlDeviceInfo.ModelName,
				chDeviceVersion = camlDeviceInfo.DeviceVersion,
				chFamilyName = camlDeviceInfo.FamilyName,
				chManufacturerName = camlDeviceInfo.ManufacturerName,
				chSerialNumber = camlDeviceInfo.SerialNumber
			}, mvDeviceInfo.SpecialInfo.stCamLInfo);
		}
		else if (DeviceTLayerType.MvGenTLCXPDevice == deviceInfo.TLayerType)
		{
			CXPDeviceInfo cXPDeviceInfo = (CXPDeviceInfo)deviceInfo;
			mvDeviceInfo.nTLayerType = (uint)cXPDeviceInfo.TLayerType;
			mvDeviceInfo.nDevTypeInfo = cXPDeviceInfo.DevTypeInfo;
			MvCCDll.MV_CXP_DEVICE_INFO obj3 = new MvCCDll.MV_CXP_DEVICE_INFO
			{
				chManufacturerInfo = cXPDeviceInfo.ManufacturerName,
				chModelName = cXPDeviceInfo.ModelName,
				chDeviceVersion = cXPDeviceInfo.DeviceVersion,
				chSerialNumber = cXPDeviceInfo.SerialNumber,
				chUserDefinedName = new byte[64]
			};
			InnerTools.StringToByteArray(cXPDeviceInfo.UserDefinedName, obj3.chUserDefinedName);
			obj3.chDeviceID = cXPDeviceInfo.DeviceID;
			obj3.chInterfaceID = cXPDeviceInfo.InterfaceID;
			MvCCDll.StructToBytes(obj3, mvDeviceInfo.SpecialInfo.stCXPInfo);
		}
		else if (DeviceTLayerType.MvGenTLCameraLinkDevice == deviceInfo.TLayerType)
		{
			CameraLinkDeviceInfo cameraLinkDeviceInfo = (CameraLinkDeviceInfo)deviceInfo;
			mvDeviceInfo.nTLayerType = (uint)cameraLinkDeviceInfo.TLayerType;
			mvDeviceInfo.nDevTypeInfo = cameraLinkDeviceInfo.DevTypeInfo;
			MvCCDll.MV_CML_DEVICE_INFO obj4 = new MvCCDll.MV_CML_DEVICE_INFO
			{
				chManufacturerInfo = cameraLinkDeviceInfo.ManufacturerName,
				chModelName = cameraLinkDeviceInfo.ModelName,
				chDeviceVersion = cameraLinkDeviceInfo.DeviceVersion,
				chSerialNumber = cameraLinkDeviceInfo.SerialNumber,
				chUserDefinedName = new byte[64]
			};
			InnerTools.StringToByteArray(cameraLinkDeviceInfo.UserDefinedName, obj4.chUserDefinedName);
			obj4.chDeviceID = cameraLinkDeviceInfo.DeviceID;
			obj4.chInterfaceID = cameraLinkDeviceInfo.InterfaceID;
			MvCCDll.StructToBytes(obj4, mvDeviceInfo.SpecialInfo.stCMLInfo);
		}
		else if (DeviceTLayerType.MvGenTLXoFDevice == deviceInfo.TLayerType)
		{
			XoFDeviceInfo xoFDeviceInfo = (XoFDeviceInfo)deviceInfo;
			mvDeviceInfo.nTLayerType = (uint)xoFDeviceInfo.TLayerType;
			mvDeviceInfo.nDevTypeInfo = xoFDeviceInfo.DevTypeInfo;
			MvCCDll.MV_XOF_DEVICE_INFO obj5 = new MvCCDll.MV_XOF_DEVICE_INFO
			{
				chManufacturerInfo = xoFDeviceInfo.ManufacturerName,
				chModelName = xoFDeviceInfo.ModelName,
				chDeviceVersion = xoFDeviceInfo.DeviceVersion,
				chSerialNumber = xoFDeviceInfo.SerialNumber,
				chUserDefinedName = new byte[64]
			};
			InnerTools.StringToByteArray(xoFDeviceInfo.UserDefinedName, obj5.chUserDefinedName);
			obj5.chDeviceID = xoFDeviceInfo.DeviceID;
			obj5.chInterfaceID = xoFDeviceInfo.InterfaceID;
			MvCCDll.StructToBytes(obj5, mvDeviceInfo.SpecialInfo.stXoFInfo);
		}
		return 0;
	}

	private static bool IsDeviceAccessibleImpl(IDeviceInfo deviceInfo, DeviceAccessMode accessMode)
	{
		if (deviceInfo == null)
		{
			throw new MvException(-2147483644, $"IsDeviceAccessibleImpl failed! ErrorCode:{-2147483644:x}");
		}
		MvCCDll.MV_CC_DEVICE_INFO mvDeviceInfo = new MvCCDll.MV_CC_DEVICE_INFO(0u);
		DeviceInfoToMv(ref mvDeviceInfo, ref deviceInfo);
		if (MvCCDll.MV_CC_IsDeviceAccessible(ref mvDeviceInfo, (uint)accessMode) != 0)
		{
			return true;
		}
		return false;
	}

	private static int SetGigEDeviceEnumTimeoutImpl(uint timeoutInMS)
	{
		return MvCCDll.MV_GIGE_SetEnumDevTimeout(timeoutInMS);
	}

	private static int SetGigEDeviceDiscoryModeImpl(uint mode)
	{
		return MvCCDll.MV_GIGE_SetDiscoveryMode(mode);
	}

	private static int GetSerialPortListImpl(out List<string> serialPortList)
	{
		serialPortList = new List<string>();
		MvCCDll.MV_CAML_SERIAL_PORT_LIST pstSerialPortList = default(MvCCDll.MV_CAML_SERIAL_PORT_LIST);
		int num = MvCCDll.MV_CAML_GetSerialPortList(ref pstSerialPortList);
		if (num == 0)
		{
			for (int i = 0; i < pstSerialPortList.nSerialPortNum; i++)
			{
				serialPortList.Add(pstSerialPortList.stSerialPort[i].strSerialPort);
			}
		}
		return num;
	}

	private static int SetEnumSerialPortsImpl(List<string> serialPortList)
	{
		if (serialPortList == null)
		{
			return -2147483644;
		}
		MvCCDll.MV_CAML_SERIAL_PORT_LIST pstSerialPortList = default(MvCCDll.MV_CAML_SERIAL_PORT_LIST);
		if (pstSerialPortList.stSerialPort == null)
		{
			pstSerialPortList.stSerialPort = new MvCCDll.MV_CAML_SERIAL_PORT[64];
		}
		if (pstSerialPortList.nReserved == null)
		{
			pstSerialPortList.nReserved = new uint[4];
		}
		pstSerialPortList.nSerialPortNum = ((serialPortList.Count > 64) ? 64u : ((uint)serialPortList.Count));
		for (int i = 0; i < serialPortList.Count && i < 64; i++)
		{
			pstSerialPortList.stSerialPort[i].strSerialPort = serialPortList[i];
		}
		return MvCCDll.MV_CAML_SetEnumSerialPorts(ref pstSerialPortList);
	}

	private static int IssueActionCommandImpl(ActionCmdInfo actionCmdInfo, out List<ActionCmdResult> actionCmdResults)
	{
		actionCmdResults = new List<ActionCmdResult>();
		MvCCDll.MV_ACTION_CMD_INFO pstActionCmdInfo = default(MvCCDll.MV_ACTION_CMD_INFO);
		MvCCDll.MV_ACTION_CMD_RESULT_LIST pstActionCmdResults = default(MvCCDll.MV_ACTION_CMD_RESULT_LIST);
		pstActionCmdInfo.nDeviceKey = actionCmdInfo.DeviceKey;
		pstActionCmdInfo.nGroupKey = actionCmdInfo.GroupKey;
		pstActionCmdInfo.nGroupMask = actionCmdInfo.GroupMask;
		pstActionCmdInfo.bActionTimeEnable = actionCmdInfo.ActionTimeEnable;
		pstActionCmdInfo.nActionTime = actionCmdInfo.ActionTime;
		pstActionCmdInfo.pBroadcastAddress = actionCmdInfo.BroadcastAddress;
		pstActionCmdInfo.nTimeOut = actionCmdInfo.TimeOut;
		pstActionCmdInfo.bSpecialNetEnable = actionCmdInfo.SpecialNetEnable;
		if (pstActionCmdInfo.bSpecialNetEnable == 1)
		{
			pstActionCmdInfo.nSpecialNetIP = BitConverter.ToUInt32(IPAddress.Parse(actionCmdInfo.SpecialNetIP).GetAddressBytes(), 0);
		}
		if (pstActionCmdInfo.nReserved == null)
		{
			pstActionCmdInfo.nReserved = new uint[14];
		}
		int num = MvCCDll.MV_GIGE_IssueActionCommand(ref pstActionCmdInfo, ref pstActionCmdResults);
		if (num != 0)
		{
			return num;
		}
		for (int i = 0; i < pstActionCmdResults.nNumResults; i++)
		{
			int num2 = Marshal.SizeOf(typeof(MvCCDll.MV_ACTION_CMD_RESULT));
			byte[] array = new byte[num2];
			Marshal.Copy(pstActionCmdResults.pResults + num2 * i, array, 0, num2);
			MvCCDll.MV_ACTION_CMD_RESULT mV_ACTION_CMD_RESULT = (MvCCDll.MV_ACTION_CMD_RESULT)MvCCDll.ByteToStruct(array, typeof(MvCCDll.MV_ACTION_CMD_RESULT));
			ActionCmdResult item = new ActionCmdResult
			{
				DeviceAddress = mV_ACTION_CMD_RESULT.strDeviceAddress,
				Status = mV_ACTION_CMD_RESULT.nStatus
			};
			actionCmdResults.Add(item);
		}
		return 0;
	}

	private static int GetMulticastStatusImpl(IDeviceInfo deviceInfo, out bool status)
	{
		status = false;
		if (deviceInfo == null)
		{
			return -2147483644;
		}
		MvCCDll.MV_CC_DEVICE_INFO mvDeviceInfo = new MvCCDll.MV_CC_DEVICE_INFO(0u);
		DeviceInfoToMv(ref mvDeviceInfo, ref deviceInfo);
		return MvCCDll.MV_GIGE_GetMulticastStatus(ref mvDeviceInfo, ref status);
	}
}
