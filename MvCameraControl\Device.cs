using System;

namespace MvCameraControl;

internal class Device : IDevice, IDisposable
{
	private IntPtr _devHandle = IntPtr.Zero;

	private IDeviceInfo _deviceInfo;

	private IStreamGrabber _streamGrabber;

	private IEventGrabber _eventGrabber;

	private IParameters _parameters;

	private IImageSaver _imageSaver;

	private IImageDecoder _imageDecoder;

	private IVideoRecorder _videoRecorder;

	private IImageProcess _imageProcess;

	private IPixelTypeConverter _pixelTypeConverter;

	private IImageRender _imageRender;

	private ByteBlockPool _byteBlockPool;

	private bool _isDisposed;

	private MvCCDll.cbExceptiondelegate _exceptionDelegate;

	protected IntPtr DevHandle => _devHandle;

	/// <summary>
	/// 判断设备是否处于连接状态
	/// </summary>
	public bool IsConnected
	{
		get
		{
			if (MvCCDll.MV_CC_IsDeviceConnected(_devHandle) != 0)
			{
				return true;
			}
			return false;
		}
	}

	/// <summary>
	/// 获取设备对应的图像采集对象
	/// </summary>
	public IStreamGrabber StreamGrabber => _streamGrabber;

	public IEventGrabber EventGrabber => _eventGrabber;

	/// <summary>
	/// 获取设备信息
	/// </summary>
	public IDeviceInfo DeviceInfo
	{
		get
		{
			if (this is IGigEDevice)
			{
				MvCCDll.MV_CC_DEVICE_INFO pstDevInfo = default(MvCCDll.MV_CC_DEVICE_INFO);
				if (MvCCDll.MV_CC_GetDeviceInfo(_devHandle, ref pstDevInfo) == 0)
				{
					GigEDeviceInfo gigEDeviceInfo = _deviceInfo as GigEDeviceInfo;
					gigEDeviceInfo.MajorVer = pstDevInfo.nMajorVer;
					gigEDeviceInfo.MinorVer = pstDevInfo.nMinorVer;
					gigEDeviceInfo.MacAddrHigh = pstDevInfo.nMacAddrHigh;
					gigEDeviceInfo.MacAddrLow = pstDevInfo.nMacAddrLow;
					gigEDeviceInfo.TLayerType = (DeviceTLayerType)pstDevInfo.nTLayerType;
					gigEDeviceInfo.DevTypeInfo = pstDevInfo.nDevTypeInfo;
					MvCCDll.MV_GIGE_DEVICE_INFO_EX mV_GIGE_DEVICE_INFO_EX = (MvCCDll.MV_GIGE_DEVICE_INFO_EX)MvCCDll.ByteToStruct(pstDevInfo.SpecialInfo.stGigEInfo, typeof(MvCCDll.MV_GIGE_DEVICE_INFO_EX));
					gigEDeviceInfo.ManufacturerName = mV_GIGE_DEVICE_INFO_EX.chManufacturerName;
					gigEDeviceInfo.ModelName = mV_GIGE_DEVICE_INFO_EX.chModelName;
					gigEDeviceInfo.DeviceVersion = mV_GIGE_DEVICE_INFO_EX.chDeviceVersion;
					gigEDeviceInfo.SerialNumber = mV_GIGE_DEVICE_INFO_EX.chSerialNumber;
					gigEDeviceInfo.UserDefinedName = InnerTools.ByteArrayToString(mV_GIGE_DEVICE_INFO_EX.chUserDefinedName);
					gigEDeviceInfo.IpCfgOption = mV_GIGE_DEVICE_INFO_EX.nIpCfgOption;
					gigEDeviceInfo.IpCfgCurrent = mV_GIGE_DEVICE_INFO_EX.nIpCfgCurrent;
					gigEDeviceInfo.CurrentIp = mV_GIGE_DEVICE_INFO_EX.nCurrentIp;
					gigEDeviceInfo.CurrentSubNetMask = mV_GIGE_DEVICE_INFO_EX.nCurrentSubNetMask;
					gigEDeviceInfo.DefultGateWay = mV_GIGE_DEVICE_INFO_EX.nDefultGateWay;
					gigEDeviceInfo.NetExport = mV_GIGE_DEVICE_INFO_EX.nNetExport;
					gigEDeviceInfo.VirtualDevice = false;
					gigEDeviceInfo.GenTLDevice = false;
					gigEDeviceInfo.HostIp = mV_GIGE_DEVICE_INFO_EX.nReserved[0];
					gigEDeviceInfo.nMulticastIp = mV_GIGE_DEVICE_INFO_EX.nReserved[2];
					gigEDeviceInfo.nMulticastPort = mV_GIGE_DEVICE_INFO_EX.nReserved[3];
					if (1 == mV_GIGE_DEVICE_INFO_EX.nReserved[1] || 16 == pstDevInfo.nTLayerType)
					{
						gigEDeviceInfo.VirtualDevice = true;
					}
					else if (2 == mV_GIGE_DEVICE_INFO_EX.nReserved[1] || 64 == pstDevInfo.nTLayerType)
					{
						gigEDeviceInfo.GenTLDevice = true;
					}
				}
			}
			return _deviceInfo;
		}
	}

	/// <summary>
	/// 获取设备对应的参数配置对象
	/// </summary>
	public IParameters Parameters => _parameters;

	public IPixelTypeConverter PixelTypeConverter => _pixelTypeConverter;

	public IImageRender ImageRender => _imageRender;

	public IImageSaver ImageSaver => _imageSaver;

	public IImageDecoder ImageDecoder => _imageDecoder;

	public IVideoRecorder VideoRecorder => _videoRecorder;

	public IImageProcess ImageProcess => _imageProcess;

	private event EventHandler<DeviceExceptionArgs> _deviceExceptionEvent;

	public event EventHandler<DeviceExceptionArgs> DeviceExceptionEvent
	{
		add
		{
			if (this._deviceExceptionEvent == null)
			{
				MvCCDll.MV_CC_RegisterExceptionCallBack(_devHandle, _exceptionDelegate, IntPtr.Zero);
			}
			_deviceExceptionEvent += value;
		}
		remove
		{
			_deviceExceptionEvent -= value;
			if (this._deviceExceptionEvent == null)
			{
				MvCCDll.MV_CC_RegisterExceptionCallBack(_devHandle, null, IntPtr.Zero);
			}
		}
	}

	public Device(IDeviceInfo deviceInfo)
	{
		_deviceInfo = deviceInfo;
		int num = CreateHandle(deviceInfo);
		if (num != 0)
		{
			throw new MvException(num, $"CreateHandle failed! ErrorCode:{num:x}");
		}
		Init();
	}

	public Device(IGenTLDevInfo gentlDevInfo)
	{
		if (gentlDevInfo == null)
		{
			throw new MvException(-2147483644, $"CreateDevice failed! ErrorCode:{-2147483644:x}");
		}
		int num = CreateHandleByGenTL(gentlDevInfo);
		if (num != 0)
		{
			throw new MvException(num, $"CreateHandle failed! ErrorCode:{num:x}");
		}
		Init();
	}

	~Device()
	{
		Dispose(disposing: false);
	}

	protected virtual void Dispose(bool disposing)
	{
		if (!_isDisposed)
		{
			if (disposing)
			{
				_byteBlockPool.ClosePool();
			}
			if (_devHandle != IntPtr.Zero)
			{
				Close();
				MvCCDll.MV_CC_DestroyHandle(_devHandle);
				_devHandle = IntPtr.Zero;
			}
			_isDisposed = true;
		}
	}

	public int Open()
	{
		return MvCCDll.MV_CC_OpenDevice(_devHandle, 1u, 0);
	}

	public int Open(DeviceAccessMode AccessMode, uint switchoverKey)
	{
		return MvCCDll.MV_CC_OpenDevice(_devHandle, (uint)AccessMode, (ushort)switchoverKey);
	}

	public int Close()
	{
		return MvCCDll.MV_CC_CloseDevice(_devHandle);
	}

	public void Dispose()
	{
		Dispose(disposing: true);
		GC.SuppressFinalize(this);
	}

	public int LocalUpgrade(string filePath)
	{
		return MvCCDll.MV_CC_LocalUpgrade(_devHandle, filePath);
	}

	public int GetUpgradeProcess(out uint process)
	{
		process = 0u;
		return MvCCDll.MV_CC_GetUpgradeProcess(_devHandle, ref process);
	}

	public int EventNotificationOn(string eventName)
	{
		return MvCCDll.MV_CC_EventNotificationOn(_devHandle, eventName);
	}

	public int EventNotificationOff(string eventName)
	{
		return MvCCDll.MV_CC_EventNotificationOff(_devHandle, eventName);
	}

	/// <summary>
	/// 初始化属性值，在构造函数中使用
	/// </summary>
	private void Init()
	{
		_exceptionDelegate = ExceptionCallback;
		_streamGrabber = new StreamGrabber(_devHandle);
		_eventGrabber = new EventGrabber(_devHandle);
		_parameters = new Parameters(_devHandle);
		_byteBlockPool = new ByteBlockPool();
		_pixelTypeConverter = new PixelTypeConverter(_devHandle, _byteBlockPool);
		_imageSaver = new ImageSaver(_devHandle);
		_imageDecoder = new ImageDecoder(_devHandle, _byteBlockPool);
		_videoRecorder = new VideoRecorder(_devHandle);
		_imageProcess = new ImageProcess(_devHandle, _byteBlockPool);
		_imageRender = new ImageRender(_devHandle);
	}

	/// <summary>
	/// 创建相机句柄
	/// </summary>
	/// <param name="deviceInfo"></param>
	/// <returns>错误码</returns>
	private int CreateHandle(IDeviceInfo deviceInfo)
	{
		MvCCDll.MV_CC_DEVICE_INFO stDevInfo = new MvCCDll.MV_CC_DEVICE_INFO(0u);
		if (DeviceTLayerType.MvGigEDevice == deviceInfo.TLayerType || DeviceTLayerType.MvGenTLGigEDevice == deviceInfo.TLayerType || DeviceTLayerType.MvVirGigEDevice == deviceInfo.TLayerType)
		{
			GigEDeviceInfo gigEDeviceInfo = deviceInfo as GigEDeviceInfo;
			stDevInfo.nMajorVer = gigEDeviceInfo.MajorVer;
			stDevInfo.nMinorVer = gigEDeviceInfo.MinorVer;
			stDevInfo.nMacAddrHigh = gigEDeviceInfo.MacAddrHigh;
			stDevInfo.nMacAddrLow = gigEDeviceInfo.MacAddrLow;
			stDevInfo.nTLayerType = (uint)gigEDeviceInfo.TLayerType;
			stDevInfo.nDevTypeInfo = gigEDeviceInfo.DevTypeInfo;
			MvCCDll.MV_GIGE_DEVICE_INFO obj = new MvCCDll.MV_GIGE_DEVICE_INFO
			{
				chDeviceVersion = gigEDeviceInfo.DeviceVersion,
				chManufacturerName = gigEDeviceInfo.ManufacturerName,
				chManufacturerSpecificInfo = gigEDeviceInfo.ManufacturerSpecificInfo,
				chModelName = gigEDeviceInfo.ModelName,
				chSerialNumber = gigEDeviceInfo.SerialNumber,
				chUserDefinedName = gigEDeviceInfo.UserDefinedName,
				nCurrentIp = gigEDeviceInfo.CurrentIp,
				nCurrentSubNetMask = gigEDeviceInfo.CurrentSubNetMask,
				nDefultGateWay = gigEDeviceInfo.DefultGateWay,
				nIpCfgCurrent = gigEDeviceInfo.IpCfgCurrent,
				nIpCfgOption = gigEDeviceInfo.IpCfgOption,
				nNetExport = gigEDeviceInfo.NetExport
			};
			if (obj.nReserved == null)
			{
				obj.nReserved = new uint[4];
			}
			obj.nReserved[0] = gigEDeviceInfo.HostIp;
			obj.nReserved[1] = 0u;
			obj.nReserved[2] = gigEDeviceInfo.nMulticastIp;
			obj.nReserved[3] = gigEDeviceInfo.nMulticastPort;
			if (gigEDeviceInfo.VirtualDevice)
			{
				obj.nReserved[1] = 1u;
			}
			else if (gigEDeviceInfo.GenTLDevice)
			{
				obj.nReserved[1] = 2u;
			}
			MvCCDll.StructToBytes(obj, stDevInfo.SpecialInfo.stGigEInfo);
		}
		else if (DeviceTLayerType.MvUsbDevice == deviceInfo.TLayerType || DeviceTLayerType.MvVirUsbDevice == deviceInfo.TLayerType)
		{
			USBDeviceInfo uSBDeviceInfo = deviceInfo as USBDeviceInfo;
			stDevInfo.nMajorVer = 0;
			stDevInfo.nMinorVer = 0;
			stDevInfo.nMacAddrHigh = 0u;
			stDevInfo.nMacAddrLow = 0u;
			stDevInfo.nTLayerType = (uint)deviceInfo.TLayerType;
			stDevInfo.nDevTypeInfo = deviceInfo.DevTypeInfo;
			MvCCDll.MV_USB3_DEVICE_INFO obj2 = new MvCCDll.MV_USB3_DEVICE_INFO
			{
				chDeviceGUID = uSBDeviceInfo.DeviceGUID,
				chDeviceVersion = uSBDeviceInfo.DeviceVersion,
				chFamilyName = uSBDeviceInfo.FamilyName,
				chManufacturerName = uSBDeviceInfo.ManufacturerName,
				chModelName = uSBDeviceInfo.ModelName,
				chSerialNumber = uSBDeviceInfo.SerialNumber,
				chUserDefinedName = uSBDeviceInfo.UserDefinedName,
				chVendorName = uSBDeviceInfo.VendorName,
				CrtlInEndPoint = uSBDeviceInfo.CrtlInEndPoint,
				CrtlOutEndPoint = uSBDeviceInfo.CrtlOutEndPoint,
				EventEndPoint = uSBDeviceInfo.EventEndPoint,
				idProduct = uSBDeviceInfo.ProductID,
				idVendor = uSBDeviceInfo.VendorID,
				nbcdUSB = uSBDeviceInfo.nbcdUSB,
				nDeviceNumber = uSBDeviceInfo.DeviceNumber,
				StreamEndPoint = uSBDeviceInfo.StreamEndPoint,
				nDeviceAddress = uSBDeviceInfo.DeviceAddress
			};
			if (obj2.nReserved == null)
			{
				obj2.nReserved = new uint[2];
			}
			obj2.nReserved[1] = 0u;
			obj2.nReserved[1] = (uSBDeviceInfo.VirtualDevice ? 1u : 0u);
			MvCCDll.StructToBytes(obj2, stDevInfo.SpecialInfo.stUsb3VInfo);
		}
		else if (DeviceTLayerType.MvCameraLinkDevice == deviceInfo.TLayerType)
		{
			CamlDeviceInfo camlDeviceInfo = (CamlDeviceInfo)deviceInfo;
			stDevInfo.nMajorVer = 0;
			stDevInfo.nMinorVer = 0;
			stDevInfo.nMacAddrHigh = 0u;
			stDevInfo.nMacAddrLow = 0u;
			stDevInfo.nTLayerType = (uint)camlDeviceInfo.TLayerType;
			MvCCDll.StructToBytes(new MvCCDll.MV_CamL_DEV_INFO
			{
				chDeviceVersion = camlDeviceInfo.DeviceVersion,
				chFamilyName = camlDeviceInfo.FamilyName,
				chManufacturerName = camlDeviceInfo.ManufacturerName,
				chModelName = camlDeviceInfo.ModelName,
				chPortID = camlDeviceInfo.PortID,
				chSerialNumber = camlDeviceInfo.SerialNumber
			}, stDevInfo.SpecialInfo.stCamLInfo);
		}
		else if (DeviceTLayerType.MvGenTLCameraLinkDevice == deviceInfo.TLayerType)
		{
			CameraLinkDeviceInfo cameraLinkDeviceInfo = (CameraLinkDeviceInfo)deviceInfo;
			stDevInfo.nTLayerType = (uint)cameraLinkDeviceInfo.TLayerType;
			stDevInfo.nDevTypeInfo = cameraLinkDeviceInfo.DevTypeInfo;
			MvCCDll.MV_CML_DEVICE_INFO obj3 = new MvCCDll.MV_CML_DEVICE_INFO
			{
				chManufacturerInfo = cameraLinkDeviceInfo.ManufacturerName,
				chModelName = cameraLinkDeviceInfo.ModelName,
				chDeviceVersion = cameraLinkDeviceInfo.DeviceVersion,
				chSerialNumber = cameraLinkDeviceInfo.SerialNumber,
				chUserDefinedName = new byte[64]
			};
			InnerTools.StringToByteArray(cameraLinkDeviceInfo.UserDefinedName, obj3.chUserDefinedName);
			obj3.chDeviceID = cameraLinkDeviceInfo.DeviceID;
			obj3.chInterfaceID = cameraLinkDeviceInfo.InterfaceID;
			MvCCDll.StructToBytes(obj3, stDevInfo.SpecialInfo.stCMLInfo);
		}
		else if (DeviceTLayerType.MvGenTLCXPDevice == deviceInfo.TLayerType)
		{
			CXPDeviceInfo cXPDeviceInfo = (CXPDeviceInfo)deviceInfo;
			stDevInfo.nTLayerType = (uint)cXPDeviceInfo.TLayerType;
			stDevInfo.nDevTypeInfo = cXPDeviceInfo.DevTypeInfo;
			MvCCDll.MV_CXP_DEVICE_INFO obj4 = new MvCCDll.MV_CXP_DEVICE_INFO
			{
				chManufacturerInfo = cXPDeviceInfo.ManufacturerName,
				chModelName = cXPDeviceInfo.ModelName,
				chDeviceVersion = cXPDeviceInfo.DeviceVersion,
				chSerialNumber = cXPDeviceInfo.SerialNumber,
				chUserDefinedName = new byte[64]
			};
			InnerTools.StringToByteArray(cXPDeviceInfo.UserDefinedName, obj4.chUserDefinedName);
			obj4.chDeviceID = cXPDeviceInfo.DeviceID;
			obj4.chInterfaceID = cXPDeviceInfo.InterfaceID;
			MvCCDll.StructToBytes(obj4, stDevInfo.SpecialInfo.stCXPInfo);
		}
		else
		{
			if (DeviceTLayerType.MvGenTLXoFDevice != deviceInfo.TLayerType)
			{
				return -2147483644;
			}
			XoFDeviceInfo xoFDeviceInfo = (XoFDeviceInfo)deviceInfo;
			stDevInfo.nTLayerType = (uint)xoFDeviceInfo.TLayerType;
			stDevInfo.nDevTypeInfo = xoFDeviceInfo.DevTypeInfo;
			MvCCDll.MV_XOF_DEVICE_INFO obj5 = new MvCCDll.MV_XOF_DEVICE_INFO
			{
				chManufacturerInfo = xoFDeviceInfo.ManufacturerName,
				chModelName = xoFDeviceInfo.ModelName,
				chDeviceVersion = xoFDeviceInfo.DeviceVersion,
				chSerialNumber = xoFDeviceInfo.SerialNumber,
				chUserDefinedName = new byte[64]
			};
			InnerTools.StringToByteArray(xoFDeviceInfo.UserDefinedName, obj5.chUserDefinedName);
			obj5.chDeviceID = xoFDeviceInfo.DeviceID;
			obj5.chInterfaceID = xoFDeviceInfo.InterfaceID;
			MvCCDll.StructToBytes(obj5, stDevInfo.SpecialInfo.stXoFInfo);
		}
		IntPtr handle = IntPtr.Zero;
		int num = MvCCDll.MV_CC_CreateHandle(ref handle, ref stDevInfo);
		if (num == 0)
		{
			_devHandle = handle;
		}
		return num;
	}

	private int CreateHandleByGenTL(IGenTLDevInfo gentlDevInfo)
	{
		MvCCDll.MV_GENTL_DEV_INFO stDevInfo = new MvCCDll.MV_GENTL_DEV_INFO
		{
			chInterfaceID = gentlDevInfo.InterfaceID,
			chDeviceID = gentlDevInfo.DeviceID,
			chVendorName = gentlDevInfo.VendorName,
			chModelName = gentlDevInfo.ModelName,
			chTLType = gentlDevInfo.TLType,
			chDisplayName = gentlDevInfo.DisplayName,
			chUserDefinedName = gentlDevInfo.UserDefinedName,
			chSerialNumber = gentlDevInfo.SerialNumber,
			chDeviceVersion = gentlDevInfo.DeviceVersion,
			nCtiIndex = gentlDevInfo.CtiIndex
		};
		IntPtr handle = IntPtr.Zero;
		int num = MvCCDll.MV_CC_CreateHandleByGenTL(ref handle, ref stDevInfo);
		if (num == 0)
		{
			_devHandle = handle;
		}
		return num;
	}

	private void ExceptionCallback(uint nMsgType, IntPtr pUser)
	{
		if (this._deviceExceptionEvent != null)
		{
			this._deviceExceptionEvent(this, new DeviceExceptionArgs((DeviceExceptionType)nMsgType));
		}
	}
}
