using System;
using System.Collections.Generic;
using System.Timers;

namespace MvCameraControl;

/// <summary>
/// 内存池
/// </summary>
internal class ByteBlockPool
{
	private Dictionary<long, LinkedList<ByteBlock>> _idleBlockDict;

	private DateTime _utcTime = new DateTime(1970, 1, 1, 0, 0, 0, 0);

	public const int _LOHMinSize = 85000;

	private const int _blockFlag = 153;

	private const double _maxIdleTime = 10000.0;

	private Timer _timer;

	private const double _timeInterval = 1000.0;

	private volatile bool _isClosed;

	public ByteBlockPool()
	{
		_idleBlockDict = new Dictionary<long, LinkedList<ByteBlock>>();
	}

	/// <summary>
	/// 关闭内存池，清空内部缓存
	/// </summary>
	public void ClosePool()
	{
		lock (this)
		{
			_idleBlockDict.Clear();
			if (_timer != null)
			{
				_timer.Close();
				_timer = null;
			}
			_isClosed = true;
		}
	}

	public ByteBlock GetByteBlock(long needSize)
	{
		if (needSize < 85000)
		{
			needSize = 85000L;
		}
		ByteBlock byteBlock = null;
		lock (this)
		{
			if (_isClosed)
			{
				return new ByteBlock(needSize);
			}
			if (_idleBlockDict.ContainsKey(needSize))
			{
				LinkedList<ByteBlock> linkedList = _idleBlockDict[needSize];
				byteBlock = linkedList.First.Value;
				linkedList.RemoveFirst();
				if (linkedList.Count == 0)
				{
					_idleBlockDict.Remove(needSize);
				}
			}
		}
		if (byteBlock == null)
		{
			byteBlock = new ByteBlock(needSize, 153);
		}
		byteBlock.RentTime = (DateTime.UtcNow - _utcTime).TotalMilliseconds;
		byteBlock.ReturnTime = 0.0;
		return byteBlock;
	}

	/// <summary>
	/// 归还内存块到内存池
	/// </summary>
	/// <param name="byteBlock">内存块</param>
	/// <param name="isDispose">是否手动释放</param>
	public void FreeByteBlock(ByteBlock byteBlock, bool isDispose)
	{
		if (!isDispose || byteBlock.Flag != 153)
		{
			return;
		}
		byteBlock.ReturnTime = (DateTime.UtcNow - _utcTime).TotalMilliseconds;
		byteBlock.RentTime = 0.0;
		lock (this)
		{
			if (!_isClosed)
			{
				if (_idleBlockDict.ContainsKey(byteBlock.Length))
				{
					_idleBlockDict[byteBlock.Length].AddFirst(byteBlock);
				}
				else
				{
					LinkedList<ByteBlock> linkedList = new LinkedList<ByteBlock>();
					linkedList.AddFirst(byteBlock);
					_idleBlockDict[byteBlock.Length] = linkedList;
				}
				if (_timer == null)
				{
					_timer = new Timer(1000.0);
					_timer.Elapsed += OnTimedEvent;
					_timer.AutoReset = true;
				}
				if (!_timer.Enabled)
				{
					_timer.Enabled = true;
				}
			}
		}
	}

	private void OnTimedEvent(object sender, ElapsedEventArgs e)
	{
		TimeSpan timeSpan = DateTime.UtcNow - _utcTime;
		lock (this)
		{
			UpdateBlockList(timeSpan.TotalMilliseconds);
			if (_idleBlockDict.Count == 0)
			{
				_timer.Enabled = false;
			}
		}
	}

	/// <summary>
	/// 更新内存块列表，释放过期内存。方法内部不加锁
	/// </summary>
	/// <param name="timesNow"></param>
	private void UpdateBlockList(double timesNow)
	{
		List<long> list = new List<long>();
		foreach (KeyValuePair<long, LinkedList<ByteBlock>> item in _idleBlockDict)
		{
			UpdateBlockListByIdleTime(item.Value, timesNow);
			if (item.Value.Count == 0)
			{
				list.Add(item.Key);
			}
		}
		foreach (long item2 in list)
		{
			_idleBlockDict.Remove(item2);
		}
	}

	/// <summary>
	/// 更新内存块的空闲时间并删除过期内存块。 方法内部不加锁
	/// </summary>
	/// <param name="blockList"></param>
	/// <param name="timesNow"></param>
	private void UpdateBlockListByIdleTime(LinkedList<ByteBlock> blockList, double timesNow)
	{
		LinkedList<ByteBlock> linkedList = new LinkedList<ByteBlock>();
		foreach (ByteBlock block in blockList)
		{
			if (timesNow - block.ReturnTime > 10000.0)
			{
				linkedList.AddLast(block);
			}
		}
		foreach (ByteBlock item in linkedList)
		{
			blockList.Remove(item);
		}
		linkedList.Clear();
	}

	/// <summary>
	/// 打印统计信息
	/// </summary>
	private void PrintPoolBlockStatics()
	{
		if (_idleBlockDict.Count == 0)
		{
			Console.WriteLine("IdleBlockDict: No blocks");
			return;
		}
		foreach (KeyValuePair<long, LinkedList<ByteBlock>> item in _idleBlockDict)
		{
			uint num = 0u;
			ulong num2 = 0uL;
			foreach (ByteBlock item2 in item.Value)
			{
				num++;
				num2 += (ulong)item2.Length;
			}
			Console.WriteLine("PrintPoolBlockStatics: IdleBlockDict: Length[{0}], BlockCount[{1}], TotoalBytes[{2}]", item.Key, num, num2);
		}
	}
}
