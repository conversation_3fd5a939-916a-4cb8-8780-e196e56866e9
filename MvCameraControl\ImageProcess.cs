using System;
using System.Collections.Generic;

namespace MvCameraControl;

internal class ImageProcess : IImageProcess
{
	private IntPtr _devHandle = IntPtr.Zero;

	private ByteBlockPool _byteBlockPool;

	internal ImageProcess(IntPtr devHandle, ByteBlockPool pool)
	{
		_devHandle = devHandle;
		_byteBlockPool = pool;
	}

	public int FlipImage(IImage inImage, out IImage outImage, ImageFlipType flipType)
	{
		outImage = null;
		if (inImage == null)
		{
			return -**********;
		}
		MvPooledImage mvPooledImage = new MvPooledImage(inImage.Width, inImage.Height, inImage.PixelType, inImage.ImageSize, _devHandle, _byteBlockPool);
		MvCCDll.MV_CC_FLIP_IMAGE_PARAM pstFlipParam = new MvCCDll.MV_CC_FLIP_IMAGE_PARAM
		{
			nWidth = inImage.Width,
			nHeight = inImage.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)inImage.PixelType,
			pSrcData = inImage.PixelDataPtr,
			nSrcDataLen = (uint)inImage.ImageSize,
			enFlipType = (MvCCDll.MV_IMG_FLIP_TYPE)flipType,
			pDstBuf = mvPooledImage.PixelDataPtr,
			nDstBufSize = (uint)mvPooledImage.ImageSize
		};
		int num = MvCCDll.MV_CC_FlipImage(_devHandle, ref pstFlipParam);
		if (num != 0)
		{
			mvPooledImage.Dispose();
			return num;
		}
		outImage = mvPooledImage;
		return num;
	}

	public int FlipImage(IntPtr srcBuffer, ImageInfo srcImageInfo, IntPtr dstBuffer, ulong dstBufferSize, ImageFlipType flipType, out ImageInfo imageInfo)
	{
		imageInfo = new ImageInfo();
		if (srcBuffer == IntPtr.Zero || srcImageInfo == null)
		{
			return -**********;
		}
		MvCCDll.MV_CC_FLIP_IMAGE_PARAM pstFlipParam = new MvCCDll.MV_CC_FLIP_IMAGE_PARAM
		{
			nWidth = srcImageInfo.Width,
			nHeight = srcImageInfo.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)srcImageInfo.PixelType,
			pSrcData = srcBuffer,
			nSrcDataLen = (uint)srcImageInfo.ImageSize,
			enFlipType = (MvCCDll.MV_IMG_FLIP_TYPE)flipType,
			pDstBuf = dstBuffer,
			nDstBufSize = (uint)dstBufferSize
		};
		int num = MvCCDll.MV_CC_FlipImage(_devHandle, ref pstFlipParam);
		imageInfo.ImageSize = pstFlipParam.nDstBufLen;
		if (num != 0)
		{
			return num;
		}
		imageInfo.Width = srcImageInfo.Width;
		imageInfo.Height = srcImageInfo.Height;
		imageInfo.PixelType = srcImageInfo.PixelType;
		imageInfo.ImageSize = pstFlipParam.nDstBufLen;
		return num;
	}

	public int ImageContrast(IImage inImage, out IImage outImage, uint contrastFactor)
	{
		outImage = null;
		if (inImage == null)
		{
			return -**********;
		}
		MvPooledImage mvPooledImage = new MvPooledImage(inImage.Width, inImage.Height, inImage.PixelType, inImage.ImageSize, _devHandle, _byteBlockPool);
		MvCCDll.MV_CC_CONTRAST_PARAM pstContrastParam = new MvCCDll.MV_CC_CONTRAST_PARAM
		{
			nWidth = inImage.Width,
			nHeight = inImage.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)inImage.PixelType,
			pSrcBuf = inImage.PixelDataPtr,
			nSrcBufLen = (uint)inImage.ImageSize,
			nContrastFactor = contrastFactor,
			pDstBuf = mvPooledImage.PixelDataPtr,
			nDstBufSize = (uint)mvPooledImage.ImageSize
		};
		int num = MvCCDll.MV_CC_ImageContrast(_devHandle, ref pstContrastParam);
		if (num != 0)
		{
			mvPooledImage.Dispose();
			return num;
		}
		outImage = mvPooledImage;
		return num;
	}

	public int ImageContrast(IntPtr srcBuffer, ImageInfo srcImageInfo, IntPtr dstBuffer, ulong dstBufferSize, uint contrastFactor, out ImageInfo imageInfo)
	{
		imageInfo = new ImageInfo();
		if (srcBuffer == IntPtr.Zero || srcImageInfo == null)
		{
			return -**********;
		}
		MvCCDll.MV_CC_CONTRAST_PARAM pstContrastParam = new MvCCDll.MV_CC_CONTRAST_PARAM
		{
			nWidth = srcImageInfo.Width,
			nHeight = srcImageInfo.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)srcImageInfo.PixelType,
			pSrcBuf = srcBuffer,
			nSrcBufLen = (uint)srcImageInfo.ImageSize,
			nContrastFactor = contrastFactor,
			pDstBuf = dstBuffer,
			nDstBufSize = (uint)dstBufferSize
		};
		int num = MvCCDll.MV_CC_ImageContrast(_devHandle, ref pstContrastParam);
		imageInfo.ImageSize = pstContrastParam.nDstBufLen;
		if (num != 0)
		{
			return num;
		}
		imageInfo.Width = srcImageInfo.Width;
		imageInfo.Height = srcImageInfo.Height;
		imageInfo.PixelType = srcImageInfo.PixelType;
		imageInfo.ImageSize = pstContrastParam.nDstBufLen;
		return num;
	}

	public int ReconstructImage(IImage inImage, uint exposureNum, ImageReconstructionMethod method, ImageStitchingMethod imageStitchingMethod, out IImage outImage)
	{
		outImage = null;
		if (inImage == null)
		{
			return -**********;
		}
		if (exposureNum == 0 || exposureNum > 8)
		{
			return -**********;
		}
		int num = 0;
		MvCCDll.MV_RECONSTRUCT_IMAGE_PARAM pstReconstructParam = new MvCCDll.MV_RECONSTRUCT_IMAGE_PARAM
		{
			nWidth = inImage.Width,
			nHeight = inImage.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)inImage.PixelType,
			pSrcData = inImage.PixelDataPtr,
			nSrcDataLen = (uint)inImage.ImageSize,
			stDstBufList = new MvCCDll.MV_OUTPUT_IMAGE_INFO[8]
		};
		uint num2 = pstReconstructParam.nHeight / exposureNum;
		if (inImage.PixelType.ToString().Contains("Bayer"))
		{
			num2 = num2 / 2 * 2;
		}
		ulong imageSize = InnerTools.GetImageSize(pstReconstructParam.nWidth, num2, inImage.PixelType);
		MvPooledImage mvPooledImage = new MvPooledImage(inImage.Width, num2 * exposureNum, inImage.PixelType, imageSize * exposureNum, _devHandle, _byteBlockPool);
		long num3 = 0L;
		for (int i = 0; i < exposureNum; i++)
		{
			pstReconstructParam.stDstBufList[i].nBufSize = (uint)imageSize;
			pstReconstructParam.stDstBufList[i].pBuf = new IntPtr(mvPooledImage.PixelDataPtr.ToInt64() + num3);
			num3 += pstReconstructParam.stDstBufList[i].nBufSize;
		}
		pstReconstructParam.nExposureNum = exposureNum;
		pstReconstructParam.enReconstructMethod = (MvCCDll.MV_IMAGE_RECONSTRUCTION_METHOD)method;
		num = MvCCDll.MV_CC_ReconstructImage(_devHandle, ref pstReconstructParam);
		if (num != 0)
		{
			mvPooledImage.Dispose();
			return num;
		}
		outImage = mvPooledImage;
		return 0;
	}

	public int ReconstructImage(IntPtr srcBuffer, ImageInfo srcImageInfo, uint exposureNum, IntPtr dstBuffer, ulong dstBufferSize, ImageReconstructionMethod method, ImageStitchingMethod imageStitchingMethod, out ImageInfo dstImageInfo)
	{
		dstImageInfo = new ImageInfo();
		if (srcBuffer == IntPtr.Zero || srcImageInfo == null)
		{
			return -**********;
		}
		if (exposureNum == 0 || exposureNum > 8)
		{
			return -**********;
		}
		uint height = srcImageInfo.Height / exposureNum;
		ulong imageSize = InnerTools.GetImageSize(srcImageInfo.Width, height, srcImageInfo.PixelType);
		ulong num = imageSize * exposureNum;
		if (dstBufferSize < num)
		{
			dstImageInfo.ImageSize = num;
			return -**********;
		}
		MvCCDll.MV_RECONSTRUCT_IMAGE_PARAM pstReconstructParam = new MvCCDll.MV_RECONSTRUCT_IMAGE_PARAM
		{
			nWidth = srcImageInfo.Width,
			nHeight = srcImageInfo.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)srcImageInfo.PixelType,
			pSrcData = srcBuffer,
			nSrcDataLen = (uint)srcImageInfo.ImageSize,
			stDstBufList = new MvCCDll.MV_OUTPUT_IMAGE_INFO[8]
		};
		long num2 = 0L;
		for (int i = 0; i < exposureNum; i++)
		{
			pstReconstructParam.stDstBufList[i].nBufSize = (uint)dstBufferSize;
			pstReconstructParam.stDstBufList[i].pBuf = new IntPtr(dstBuffer.ToInt64() + num2);
			num2 += (long)imageSize;
		}
		pstReconstructParam.nExposureNum = exposureNum;
		pstReconstructParam.enReconstructMethod = (MvCCDll.MV_IMAGE_RECONSTRUCTION_METHOD)method;
		int result = MvCCDll.MV_CC_ReconstructImage(_devHandle, ref pstReconstructParam);
		dstImageInfo.Width = srcImageInfo.Width;
		dstImageInfo.PixelType = srcImageInfo.PixelType;
		for (int j = 0; j < exposureNum; j++)
		{
			dstImageInfo.Height += pstReconstructParam.stDstBufList[j].nHeight;
			dstImageInfo.ImageSize += pstReconstructParam.stDstBufList[j].nBufLen;
		}
		return result;
	}

	public int ReconstructImage(IImage inImage, uint exposureNum, ImageReconstructionMethod method, out List<IImage> outImages)
	{
		outImages = new List<IImage>();
		if (inImage == null)
		{
			return -**********;
		}
		if (exposureNum == 0 || exposureNum > 8)
		{
			return -**********;
		}
		uint height = ((inImage.Height % exposureNum == 0) ? (inImage.Height / exposureNum) : (inImage.Height / exposureNum + 1));
		ulong imageSize = InnerTools.GetImageSize(inImage.Width, height, inImage.PixelType);
		List<MvPooledImage> list = new List<MvPooledImage>((int)exposureNum);
		for (int i = 0; i < exposureNum; i++)
		{
			MvPooledImage item = new MvPooledImage(inImage.Width, height, inImage.PixelType, imageSize, _devHandle, _byteBlockPool);
			list.Add(item);
		}
		MvCCDll.MV_RECONSTRUCT_IMAGE_PARAM pstReconstructParam = new MvCCDll.MV_RECONSTRUCT_IMAGE_PARAM
		{
			nWidth = inImage.Width,
			nHeight = inImage.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)inImage.PixelType,
			pSrcData = inImage.PixelDataPtr,
			nSrcDataLen = (uint)inImage.ImageSize,
			stDstBufList = new MvCCDll.MV_OUTPUT_IMAGE_INFO[8]
		};
		for (int j = 0; j < exposureNum; j++)
		{
			pstReconstructParam.stDstBufList[j].nBufSize = (uint)list[j].ImageSize;
			pstReconstructParam.stDstBufList[j].pBuf = list[j].PixelDataPtr;
		}
		pstReconstructParam.nExposureNum = exposureNum;
		pstReconstructParam.enReconstructMethod = (MvCCDll.MV_IMAGE_RECONSTRUCTION_METHOD)method;
		int num = MvCCDll.MV_CC_ReconstructImage(_devHandle, ref pstReconstructParam);
		if (num != 0)
		{
			foreach (MvPooledImage item2 in list)
			{
				item2.Dispose();
			}
			list.Clear();
			return num;
		}
		for (int k = 0; k < exposureNum; k++)
		{
			list[k].Width = pstReconstructParam.stDstBufList[k].nWidth;
			list[k].Height = pstReconstructParam.stDstBufList[k].nHeight;
			list[k].ImageSize = pstReconstructParam.stDstBufList[k].nBufLen;
			list[k].PixelType = (MvGvspPixelType)pstReconstructParam.stDstBufList[k].enPixelType;
			outImages.Add(list[k]);
		}
		return 0;
	}

	public int ReconstructImage(IntPtr srcBuffer, ImageInfo srcImageInfo, uint exposureNum, List<IntPtr> dstBuffers, ulong dstBufferSize, ImageReconstructionMethod method, out List<ImageInfo> dstImageInfos)
	{
		dstImageInfos = new List<ImageInfo>();
		if (srcBuffer == IntPtr.Zero || srcImageInfo == null)
		{
			return -**********;
		}
		if (exposureNum == 0 || exposureNum > 8)
		{
			return -**********;
		}
		MvCCDll.MV_RECONSTRUCT_IMAGE_PARAM pstReconstructParam = new MvCCDll.MV_RECONSTRUCT_IMAGE_PARAM
		{
			nWidth = srcImageInfo.Width,
			nHeight = srcImageInfo.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)srcImageInfo.PixelType,
			pSrcData = srcBuffer,
			nSrcDataLen = (uint)srcImageInfo.ImageSize,
			stDstBufList = new MvCCDll.MV_OUTPUT_IMAGE_INFO[8]
		};
		for (int i = 0; i < exposureNum; i++)
		{
			if (dstBuffers != null && i < dstBuffers.Count)
			{
				pstReconstructParam.stDstBufList[i].pBuf = dstBuffers[i];
			}
			else
			{
				pstReconstructParam.stDstBufList[i].pBuf = IntPtr.Zero;
			}
			pstReconstructParam.stDstBufList[i].nBufSize = (uint)dstBufferSize;
		}
		pstReconstructParam.nExposureNum = exposureNum;
		pstReconstructParam.enReconstructMethod = (MvCCDll.MV_IMAGE_RECONSTRUCTION_METHOD)method;
		int result = MvCCDll.MV_CC_ReconstructImage(_devHandle, ref pstReconstructParam);
		for (int j = 0; j < exposureNum; j++)
		{
			ImageInfo imageInfo = new ImageInfo();
			imageInfo.Width = pstReconstructParam.stDstBufList[j].nWidth;
			imageInfo.Height = pstReconstructParam.stDstBufList[j].nHeight;
			imageInfo.ImageSize = pstReconstructParam.stDstBufList[j].nBufLen;
			imageInfo.PixelType = (MvGvspPixelType)pstReconstructParam.stDstBufList[j].enPixelType;
			dstImageInfos.Add(imageInfo);
		}
		return result;
	}

	public int RotateImage(IImage inImage, out IImage outImage, ImageRotateAngle angle)
	{
		outImage = null;
		if (inImage == null)
		{
			return -**********;
		}
		MvPooledImage mvPooledImage = new MvPooledImage(inImage.Width, inImage.Height, inImage.PixelType, inImage.ImageSize, _devHandle, _byteBlockPool);
		MvCCDll.MV_CC_ROTATE_IMAGE_PARAM pstRotateParam = new MvCCDll.MV_CC_ROTATE_IMAGE_PARAM
		{
			nWidth = inImage.Width,
			nHeight = inImage.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)inImage.PixelType,
			pSrcData = inImage.PixelDataPtr,
			nSrcDataLen = (uint)inImage.ImageSize,
			enRotationAngle = (MvCCDll.MV_IMG_ROTATION_ANGLE)angle,
			pDstBuf = mvPooledImage.PixelDataPtr,
			nDstBufSize = (uint)mvPooledImage.ImageSize
		};
		int num = MvCCDll.MV_CC_RotateImage(_devHandle, ref pstRotateParam);
		if (num != 0)
		{
			mvPooledImage.Dispose();
			return num;
		}
		mvPooledImage.Width = pstRotateParam.nWidth;
		mvPooledImage.Height = pstRotateParam.nHeight;
		outImage = mvPooledImage;
		return num;
	}

	public int RotateImage(IntPtr srcBuffer, ImageInfo srcImageInfo, IntPtr dstBuffer, ulong dstBufferSize, ImageRotateAngle angle, out ImageInfo imageInfo)
	{
		imageInfo = new ImageInfo();
		if (srcBuffer == IntPtr.Zero || srcImageInfo == null)
		{
			return -**********;
		}
		MvCCDll.MV_CC_ROTATE_IMAGE_PARAM pstRotateParam = new MvCCDll.MV_CC_ROTATE_IMAGE_PARAM
		{
			nWidth = srcImageInfo.Width,
			nHeight = srcImageInfo.Height,
			enPixelType = (MvCCDll.MvGvspPixelType)srcImageInfo.PixelType,
			pSrcData = srcBuffer,
			nSrcDataLen = (uint)srcImageInfo.ImageSize,
			enRotationAngle = (MvCCDll.MV_IMG_ROTATION_ANGLE)angle,
			pDstBuf = dstBuffer,
			nDstBufSize = (uint)dstBufferSize
		};
		int num = MvCCDll.MV_CC_RotateImage(_devHandle, ref pstRotateParam);
		imageInfo.ImageSize = pstRotateParam.nDstBufLen;
		if (num != 0)
		{
			return num;
		}
		imageInfo.Width = pstRotateParam.nWidth;
		imageInfo.Height = pstRotateParam.nHeight;
		imageInfo.PixelType = srcImageInfo.PixelType;
		return num;
	}
}
