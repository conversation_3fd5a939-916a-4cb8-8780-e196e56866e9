using System;

namespace MvCameraControl;

/// <summary>
/// 提供图像渲染、图形绘制接口
/// </summary>
public interface IImageRender
{
	/// <summary>
	/// 显示一帧图像 
	/// </summary>
	/// <param name="hWnd">窗口句柄</param>
	/// <param name="image">图像信息</param>
	/// <param name="mode">渲染模式</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 渲染模式为RenderMode.OPENGL时，支持4G以上超大图渲染；图像大小超过4G时，仅支持PixelType_Gvsp_RGB8_Packed，PixelType_Gvsp_BGR8_Packed，PixelType_Gvsp_Mono8格式
	/// 渲染模式为RenderMode.D3D时，支持的最大分辨率为16384 * 163840
	/// </remarks>
	int DisplayOneFrame(IntPtr hWnd, IImage image, RenderMode mode = RenderMode.Default);

	/// <summary>
	/// 显示一帧图像
	/// </summary>
	/// <param name="hWnd">窗口句柄</param>
	/// <param name="imageDataPtr">图像数据指针</param>
	/// <param name="imageLen">图像数据长度</param>
	/// <param name="width">图像宽</param>
	/// <param name="height">图像高</param>
	/// <param name="PixelType">像素格式</param>
	/// <param name="mode">渲染模式</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 渲染模式为RenderMode.OPENGL时，支持4G以上超大图渲染；图像大小超过4G时，仅支持PixelType_Gvsp_RGB8_Packed，PixelType_Gvsp_BGR8_Packed，PixelType_Gvsp_Mono8格式
	/// 渲染模式为RenderMode.D3D时，支持的最大分辨率为16384 * 163840
	/// </remarks>
	int DisplayOneFrame(IntPtr hWnd, IntPtr imageDataPtr, uint imageLen, uint width, uint height, MvGvspPixelType PixelType, RenderMode mode = RenderMode.Default);

	/// <summary>
	/// 显示一帧图像
	/// </summary>
	/// <param name="hWnd">窗口句柄</param>
	/// <param name="imageDataPtr">图像数据指针</param>
	/// <param name="imageInfo">图像信息</param>
	/// <param name="mode">渲染模式</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 不支持4G以上图像渲染
	/// 渲染模式为RenderMode.D3D时，支持的最大分辨率为16384 * 163840
	/// </remarks>
	int DisplayOneFrame(IntPtr hWnd, IntPtr imageDataPtr, ImageInfo imageInfo, RenderMode mode = RenderMode.Default);

	/// <summary>
	/// 在图像上绘制矩形 
	/// </summary>
	/// <param name="rect">矩形框</param>
	/// <param name="color">线条颜色</param>
	/// <param name="lineWidth">线条宽度，只能是1或2</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	int DrawRect(MvRect rect, MvColor color, uint lineWidth);

	/// <summary>
	/// 在图像上绘制圆形 
	/// </summary>
	/// <param name="circle">圆形信息</param>
	/// <param name="color">线条颜色</param>
	/// <param name="lineWidth">线条宽度，只能是1或2</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	int DrawCircle(MvCircle circle, MvColor color, uint lineWidth);

	/// <summary>
	/// 在图像上绘制线条 
	/// </summary>
	/// <param name="line">线条信息</param>
	/// <param name="color">线条颜色</param>
	/// <param name="lineWidth">线条宽度，只能是1或2</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	int DrawLine(MvLine line, MvColor color, uint lineWidth);
}
