namespace MvCameraControl;

/// <summary>
/// USB设备信息类
/// </summary>
internal class USBDeviceInfo : IUSBDeviceInfo, IDeviceInfo
{
	/// <summary>
	/// 设备接口类型
	/// </summary>
	public DeviceTLayerType TLayerType { get; set; }

	/// <summary>
	/// 制造商信息
	/// </summary>
	public string ManufacturerName { get; set; }

	/// <summary>
	/// 设备型号
	/// </summary>
	public string ModelName { get; set; }

	/// <summary>
	/// 设备版本
	/// </summary>
	public string DeviceVersion { get; set; }

	/// <summary>
	/// 设备序列号
	/// </summary>
	public string SerialNumber { get; set; }

	/// <summary>
	/// 用户自定义名称
	/// </summary>
	public string UserDefinedName { get; set; }

	/// <summary>
	/// 设备类型信息，7 - 0 bit: 预留，15 - 8 bit：产品子类别，23 - 16 bit：产品类型，31 - 24bit：产品线（如: 0x01 标准产品；0x02 3D产品；0x03 智能ID产品）
	/// </summary>
	public uint DevTypeInfo { get; set; }

	/// <summary>
	/// 控制输入端点
	/// </summary>
	public byte CrtlInEndPoint { get; set; }

	/// <summary>
	/// 控制输出端点
	/// </summary>
	public byte CrtlOutEndPoint { get; set; }

	/// <summary>
	/// 流端点
	/// </summary>
	public byte StreamEndPoint { get; set; }

	/// <summary>
	/// 事件端点 
	/// </summary>
	public byte EventEndPoint { get; set; }

	/// <summary>
	/// 供应商ID号 
	/// </summary>
	public ushort VendorID { get; set; }

	/// <summary>
	/// 产品ID号 
	/// </summary>
	public ushort ProductID { get; set; }

	/// <summary>
	/// 设备索引号
	/// </summary>
	public uint DeviceNumber { get; set; }

	/// <summary>
	/// 设备GUID号
	/// </summary>
	public string DeviceGUID { get; set; }

	/// <summary>
	/// 家族名字
	/// </summary>
	public string FamilyName { get; set; }

	/// <summary>
	/// 供应商名称
	/// </summary>
	public string VendorName { get; set; }

	/// <summary>
	/// 支持的USB协议
	/// </summary>
	public uint nbcdUSB { get; set; }

	/// <summary>
	/// 设备地址
	/// </summary>
	public uint DeviceAddress { get; set; }

	/// <summary>
	/// 是否虚拟相机
	/// </summary>
	public bool VirtualDevice { get; set; }
}
