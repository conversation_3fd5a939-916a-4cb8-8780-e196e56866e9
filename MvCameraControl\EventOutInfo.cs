using System.Runtime.InteropServices;

namespace MvCameraControl;

internal class EventOutInfo : IEventOutInfo
{
	/// <summary>
	/// Event名称
	/// </summary>
	public string EventName { get; set; }

	/// <summary>
	/// EventID
	/// </summary>
	public ushort EventID { get; set; }

	/// <summary>
	/// 流通道序号 
	/// </summary>
	public ushort StreamChannel { get; set; }

	/// <summary>
	/// 帧号
	/// </summary>
	public ulong BlockId { get; set; }

	/// <summary>
	/// 时间戳
	/// </summary>
	public ulong Timestamp { get; set; }

	/// <summary>
	/// Event数据长度
	/// </summary>
	public uint EventDataSize { get; set; }

	/// <summary>
	/// Event数据
	/// </summary>
	public byte[] EventData { get; set; }

	public EventOutInfo(MvCCDll.MV_EVENT_OUT_INFO eventOutInfo)
	{
		EventName = eventOutInfo.EventName;
		EventID = eventOutInfo.nEventID;
		StreamChannel = eventOutInfo.nStreamChannel;
		BlockId = ((ulong)eventOutInfo.nBlockIdHigh << 32) + eventOutInfo.nBlockIdLow;
		Timestamp = ((ulong)eventOutInfo.nTimestampHigh << 32) + eventOutInfo.nTimestampLow;
		EventDataSize = eventOutInfo.nEventDataSize;
		EventData = new byte[eventOutInfo.nEventDataSize];
		if (eventOutInfo.nEventDataSize != 0)
		{
			Marshal.Copy(eventOutInfo.pEventData, EventData, 0, (int)eventOutInfo.nEventDataSize);
		}
	}
}
