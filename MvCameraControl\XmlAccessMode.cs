namespace MvCameraControl;

/// <summary>
/// Xml节点访问模式
/// </summary>
public enum XmlAccessMode
{
	/// <summary>
	/// 不可实现 
	/// </summary>
	NI,
	/// <summary>
	/// 不可用 
	/// </summary>
	NA,
	/// <summary>
	/// 只写 
	/// </summary>
	WO,
	/// <summary>
	/// 只读 
	/// </summary>
	RO,
	/// <summary>
	/// 读写 
	/// </summary>
	RW,
	/// <summary>
	/// 未定义 
	/// </summary>
	Undefined,
	/// <summary>
	/// 内部用于AccessMode循环检测
	/// </summary>
	CycleDetect
}
