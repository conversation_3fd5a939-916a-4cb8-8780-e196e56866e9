using System;
using System.Collections.Generic;

namespace MvCameraControl;

internal class Interface : IInterface, IDisposable
{
	private IntPtr _ifHandle = IntPtr.Zero;

	private IParameters _parameters;

	private IEventGrabber _eventGrabber;

	private bool _isDisposed;

	/// <summary>
	/// 获取采集卡对应的参数配置对象
	/// </summary>
	public IParameters Parameters => _parameters;

	public IEventGrabber EventGrabber => _eventGrabber;

	public Interface(IntPtr handle)
	{
		_ifHandle = handle;
		_parameters = new Parameters(_ifHandle);
		_eventGrabber = new EventGrabber(_ifHandle);
	}

	/// <summary>
	/// 打开采集卡
	/// </summary>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int Open()
	{
		return MvCCDll.MV_CC_OpenInterface(_ifHandle, "");
	}

	/// <summary>
	/// 关闭采集卡
	/// </summary>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int Close()
	{
		return MvCCDll.MV_CC_CloseInterface(_ifHandle);
	}

	/// <summary>
	/// 设备本地升级 
	/// </summary>
	/// <param name="filePath">升级文件路径</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 通过该接口可以将升级固件文件发送给设备进行升级。该接口需要等待升级固件文件成功传给设备端之后再返回，响应时间可能较长。 
	/// </remarks>
	public int LocalUpgrade(string filePath)
	{
		return MvCCDll.MV_CC_LocalUpgrade(_ifHandle, filePath);
	}

	/// <summary>
	/// 获取升级进度 
	/// </summary>
	/// <param name="process">升级进度</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int GetUpgradeProcess(out uint process)
	{
		process = 0u;
		return MvCCDll.MV_CC_GetUpgradeProcess(_ifHandle, ref process);
	}

	/// <summary>
	/// 枚举采集卡上的相机
	/// </summary>
	/// <param name="devInfoList">相机列表</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	public int EnumDevices(out List<IDeviceInfo> devInfoList)
	{
		devInfoList = new List<IDeviceInfo>();
		int num = 0;
		try
		{
			MvCCDll.MV_CC_DEVICE_INFO_LIST pstDevInfoList = default(MvCCDll.MV_CC_DEVICE_INFO_LIST);
			num = MvCCDll.MV_CC_EnumDevicesByInterface(_ifHandle, ref pstDevInfoList);
			if (num == 0)
			{
				InnerTools.DevInfoListStruct2DevInfoList(pstDevInfoList, ref devInfoList);
			}
		}
		catch (Exception)
		{
			num = -2147483642;
		}
		return num;
	}

	public int EventNotificationOn(string eventName)
	{
		return MvCCDll.MV_CC_EventNotificationOn(_ifHandle, eventName);
	}

	public int EventNotificationOff(string eventName)
	{
		return MvCCDll.MV_CC_EventNotificationOff(_ifHandle, eventName);
	}

	public void Dispose()
	{
		Dispose(disposing: true);
		GC.SuppressFinalize(this);
	}

	protected virtual void Dispose(bool disposing)
	{
		if (!_isDisposed)
		{
			if (_ifHandle != IntPtr.Zero)
			{
				MvCCDll.MV_CC_DestroyInterface(_ifHandle);
				_ifHandle = IntPtr.Zero;
			}
			_isDisposed = true;
		}
	}

	~Interface()
	{
		Dispose(disposing: false);
	}
}
