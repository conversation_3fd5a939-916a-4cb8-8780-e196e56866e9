using System;

namespace MvCameraControl;

/// <summary>
/// Chunk数据
/// </summary>
public interface IChunkData
{
	/// <summary>
	/// Chunk数据指针（非托管内存）
	/// </summary>
	IntPtr DataPtr { get; }

	/// <summary>
	/// Chunk数据，内部会进行一次拷贝，将非托管内存拷贝到托管内存
	/// </summary>
	byte[] Data { get; }

	/// <summary>
	/// ChunkID
	/// </summary>
	uint ChunkID { get; }

	/// <summary>
	/// Chunk数据长度
	/// </summary>
	uint Length { get; }
}
