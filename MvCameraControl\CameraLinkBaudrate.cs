using System;

namespace MvCameraControl;

/// <summary>
/// Camera Link波特率
/// </summary>
[Flags]
public enum CameraLinkBaudrate
{
	/// <summary>
	/// 9600
	/// </summary>
	B9600 = 1,
	/// <summary>
	/// 19200
	/// </summary>
	B19200 = 2,
	/// <summary>
	/// 38400
	/// </summary>
	B38400 = 4,
	/// <summary>
	/// 57600
	/// </summary>
	B57600 = 8,
	/// <summary>
	/// 115200
	/// </summary>
	B115200 = 0x10,
	/// <summary>
	/// 230400
	/// </summary>
	B230400 = 0x20,
	/// <summary>
	/// 460800
	/// </summary>
	B460800 = 0x40,
	/// <summary>
	/// 921600
	/// </summary>
	B921600 = 0x80,
	/// <summary>
	/// 最大值
	/// </summary>
	AutoMax = 0x40000000
}
