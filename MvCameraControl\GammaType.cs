namespace MvCameraControl;

/// <summary>
/// Gamma类型
/// </summary>
public enum GammaType
{
	/// <summary>
	/// 不启用 
	/// </summary>
	None,
	/// <summary>
	/// Gamma值 
	/// </summary>
	Value,
	/// <summary>
	/// Gamma曲线
	/// 8位，长度：256*sizeof(unsigned char) 
	/// 10位，长度：1024*sizeof(unsigned short)
	/// 12位，长度：4096*sizeof(unsigned short)
	/// 16位，长度：65536*sizeof(unsigned short) 
	/// </summary>
	UserCurve,
	/// <summary>
	/// linear RGB to sRGB 
	/// </summary>
	LRGB2SRGB,
	/// <summary>
	/// sRGB to linear RGB（仅色彩插值时支持，色彩校正时无效）
	/// </summary>
	SRGB2LRGB
}
