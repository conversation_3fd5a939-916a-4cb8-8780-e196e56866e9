using System;

namespace MvCameraControl;

internal static class InterfaceFactoryImpl
{
	public static IInterface CreateInterface(IInterfaceInfo interfaceInfo)
	{
		if (interfaceInfo == null)
		{
			throw new MvException(-2147483644, $"CreateHandle failed paramer is null! ErrorCode:{-2147483644:x}");
		}
		MvCCDll.MV_INTERFACE_INFO pInterfaceInfo = new MvCCDll.MV_INTERFACE_INFO
		{
			nTLayerType = (uint)interfaceInfo.TLayerType,
			nPCIEInfo = interfaceInfo.PCIEInfo,
			chDeviceVersion = interfaceInfo.DeviceVersion,
			chDisplayName = interfaceInfo.DisplayName,
			chInterfaceID = interfaceInfo.InterfaceID,
			chManufacturer = interfaceInfo.Manufacturer,
			chModelName = interfaceInfo.ModelName,
			chSerialNumber = interfaceInfo.SerialNumber,
			chUserDefinedName = new byte[64]
		};
		InnerTools.StringToByteArray(interfaceInfo.UserDefinedName, pInterfaceInfo.chUserDefinedName);
		IntPtr handle = IntPtr.Zero;
		int num = MvCCDll.MV_CC_CreateInterface(ref handle, ref pInterfaceInfo);
		if (num != 0)
		{
			throw new MvException(num, $"CreateHandle failed! ErrorCode:{num:x}");
		}
		return new Interface(handle);
	}

	public static IInterface CreateInterface(string interfaceID)
	{
		IntPtr handle = IntPtr.Zero;
		int num = MvCCDll.MV_CC_CreateInterfaceByID(ref handle, interfaceID);
		if (num != 0)
		{
			throw new MvException(num, $"CreateHandle failed! ErrorCode:{num:x}");
		}
		return new Interface(handle);
	}
}
