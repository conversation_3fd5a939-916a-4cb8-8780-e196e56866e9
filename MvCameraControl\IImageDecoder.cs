using System;

namespace MvCameraControl;

/// <summary>
/// 图像解码
/// </summary>
public interface IImageDecoder
{
	/// <summary>
	/// 无损解码 
	/// </summary>
	/// <param name="inFrameInfo">输入图像及帧信息</param>
	/// <param name="outFrameInfo">输出图像及帧信息。图像使用完之后需调用Dispose方法及时释放内存，防止内存快速上涨。</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 将从相机中取到的无损压缩码流解码成裸数据，同时支持解析当前相机实时图像的水印信息（如果输入的无损码流不是当前相机或者不是实时取流的，则水印解析可能异常)。
	/// 若解码失败，请检查以下情况：（1）需要CPU支持 SSE AVX指令集（2）若当前帧异常（丢包等），可能导致解码异常（3）相机出图异常，即使不丢包也会异常。 
	/// </remarks>
	int HBDecode(IFrameOut inFrameInfo, out IFrameOut outFrameInfo);

	/// <summary>
	/// 无损解码
	/// </summary>
	/// <param name="inFrameInfo">输入图像及帧信息</param>
	/// <param name="dstBuffer">输出图像的缓存</param>
	/// <param name="dstBufferSize">输出图像的缓存大小</param>
	/// <param name="imageInfo">解码后的图像信息</param>
	/// <param name="frameSpecInfo">解码后的水印信息</param>
	/// <returns>成功，返回MV_OK；失败，返回错误码</returns>
	/// <remarks>
	/// 将从相机中取到的无损压缩码流解码成裸数据，同时支持解析当前相机实时图像的水印信息（如果输入的无损码流不是当前相机或者不是实时取流的，则水印解析可能异常)。
	/// 若解码失败，请检查以下情况：（1）需要CPU支持 SSE AVX指令集（2）若当前帧异常（丢包等），可能导致解码异常（3）相机出图异常，即使不丢包也会异常。 
	/// </remarks>
	int HBDecode(IFrameOut inFrameInfo, IntPtr dstBuffer, ulong dstBufferSize, out ImageInfo imageInfo, out FrameSpecInfo frameSpecInfo);
}
