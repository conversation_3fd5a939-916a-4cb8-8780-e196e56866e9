namespace MvCameraControl;

/// <summary>
/// 采集卡信息
/// </summary>
public interface IInterfaceInfo
{
	/// <summary>
	/// 采集卡接口类型，<see cref="T:MvCameraControl.InterfaceTLayerType" />
	/// </summary>
	InterfaceTLayerType TLayerType { get; }

	/// <summary>
	/// 采集卡的PCIE插槽信息 
	/// </summary>
	uint PCIEInfo { get; }

	/// <summary>
	/// 采集卡ID 
	/// </summary>
	string InterfaceID { get; }

	/// <summary>
	/// 显示名称
	/// </summary>
	string DisplayName { get; }

	/// <summary>
	/// 序列号
	/// </summary>
	string SerialNumber { get; }

	/// <summary>
	/// 型号
	/// </summary>
	string ModelName { get; }

	/// <summary>
	///  厂商
	/// </summary>
	string Manufacturer { get; }

	/// <summary>
	/// 版本号
	/// </summary>
	string DeviceVersion { get; }

	/// <summary>
	/// 自定义名称
	/// </summary>
	string UserDefinedName { get; }
}
