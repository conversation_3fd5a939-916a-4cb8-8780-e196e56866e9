namespace MvCameraControl;

/// <summary>
/// GigE设备网络传输的相关信息
/// </summary>
public struct NetTransInfo
{
	/// <summary>
	/// 已接收数据大小 [统计StartGrabbing和StopGrabbing之间的数据量] 
	/// </summary>
	public long ReceiveDataSize;

	/// <summary>
	/// 丢失的包数量
	/// </summary>
	public long LostPacketCount;

	/// <summary>
	/// 丢帧数量
	/// </summary>
	public int LostFrameCount;

	/// <summary>
	/// 已接收的帧数 
	/// </summary>
	public uint NetRecvFrameCount;

	/// <summary>
	/// 请求重发包数 
	/// </summary>
	public ulong RequestResendPacketCount;

	/// <summary>
	/// 重发包数 
	/// </summary>
	public ulong ResendPacketCount;
}
